// import React from 'react';
// import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, <PERSON><PERSON>hart, Bar, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
// import { IoWarningOutline } from "react-icons/io5";
// import { FaArrowTrendUp, FaLocationDot } from "react-icons/fa6";
// import { MdOutlineWatchLater } from "react-icons/md";
// const TestComponent = () => {
//     // Time-based Alert Analysis Data
//     const timeBasedData = [
//         { time: '06:00', 'Shift A (Peak at Hour 6-8 alert)': 2, 'Area 2': 1, 'Area 3': 1.5 },
//         { time: '08:00', 'Shift A (Peak at Hour 6-8 alert)': 3, 'Area 2': 2, 'Area 3': 2.5 },
//         { time: '10:00', 'Shift A (Peak at Hour 6-8 alert)': 4, 'Area 2': 3, 'Area 3': 3.5 },
//         { time: '12:00', 'Shift A (Peak at Hour 6-8 alert)': 6, 'Area 2': 5, 'Area 3': 4.5 },
//         { time: '14:00', 'Shift A (Peak at Hour 6-8 alert)': 8, 'Area 2': 7, 'Area 3': 6 },
//         { time: '16:00', 'Shift A (Peak at Hour 6-8 alert)': 10, 'Area 2': 9, 'Area 3': 8 },
//         { time: '18:00', 'Shift A (Peak at Hour 6-8 alert)': 8, 'Area 2': 7, 'Area 3': 6 },
//         { time: '20:00', 'Shift A (Peak at Hour 6-8 alert)': 6, 'Area 2': 5, 'Area 3': 4 },
//         { time: '22:00', 'Shift A (Peak at Hour 6-8 alert)': 4, 'Area 2': 3, 'Area 3': 2.5 },
//         { time: '24:00', 'Shift A (Peak at Hour 6-8 alert)': 2, 'Area 2': 1, 'Area 3': 1 }
//     ];

//     // Area-based Alert Analysis Data
//     const areaBasedData = [
//         { area: 'Production Floor', alerts: 25 },
//         { area: 'Warehouse', alerts: 15 },
//         { area: 'Loading Dock', alerts: 12 },
//         { area: 'Quality Control', alerts: 8 },
//         { area: 'Maintenance', alerts: 6 },
//         { area: 'Office', alerts: 3 },
//         { area: 'Cafeteria', alerts: 2 }
//     ];

//     // Pie Chart Data
//     const pieData = [
//         { name: 'Emergency Exit', value: 35, color: '#dc3545' },
//         { name: 'Helmet', value: 25, color: '#17a2b8' },
//         { name: 'MMHE', value: 20, color: '#ffc107' },
//         { name: 'Vest', value: 15, color: '#28a745' },
//     ];

//     // Module Details Data
//     const moduleDetails = [
//         { name: 'Helmet', status: 'Operational', count: '12 active sensors', color: '#6f42c1' },
//         { name: 'Vest', status: 'Alert', count: '45 active (25.4%)', color: '#28a745' },
//         { name: 'Forklift', status: 'Operational', count: '35 active (8.5%)', color: '#ffc107' },
//         { name: 'Emergency Exit', status: 'Alert', count: '50 active (10.3%)', color: '#dc3545' }
//     ];

//     return (
//         <div className='d-flex justify-content-center'>
//             <div style={{ maxWidth: '1000px' }}>
//                 {/* Bootstrap CSS */}
//                 <link
//                     href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css"
//                     rel="stylesheet"
//                 />

//                 <div className="container-fluid p-4" style={{ backgroundColor: '#f8f9fa', minHeight: '100vh', padding: '0 60px' }}>
//                     {/* Header */}
//                     <div className="text-center mb-4">
//                         <h2 className="fw-bold">Safety Shift A Report</h2>
//                         <p className="text-muted">Comprehensive Analysis of Safety Alerts and Compliance Metrics</p>
//                         <div className="d-flex justify-content-center gap-4 text-muted small">
//                             <span>📅 Report Date: June 10, 2024</span>
//                             <span>⏰ Reporting Period: Shift A</span>
//                             <span>📊 Generated: 6:40:00 PM</span>
//                         </div>
//                     </div>
//                     <div>
//                         <hr />
//                     </div>

//                     {/* Executive Summary */}
//                     <div className=" mb-4 border-none">
//                         <div className="mb-3">
//                             <h5 className="mb-0"> <span className='fw-bold text-primary'>|</span> Executive Summary</h5>
//                         </div>
//                         <div className="">
//                             <div className="row text-center">
//                                 <div className="col-md-3 mb-3">
//                                     <ExecutiveCard
//                                         title={'Total Alerts'}
//                                         value={'77%'}
//                                         color={'#fff5f5'}
//                                         Icon={IoWarningOutline}
//                                         textColor={'text-danger'}
//                                     />
//                                 </div>
//                                 <div className="col-md-3 mb-3">
//                                     <ExecutiveCard
//                                         title={'Site Compliance'}
//                                         value={'87%'}
//                                         color={'#f0f9f0'}
//                                         Icon={FaArrowTrendUp}
//                                         textColor={'text-success'}
//                                     />
//                                 </div>
//                                 <div className="col-md-3 mb-3">

//                                     <ExecutiveCard
//                                         title={'Peak Alert Time'}
//                                         value={'10 AM'}
//                                         color={'#f0f8ff'}
//                                         Icon={MdOutlineWatchLater}
//                                         textColor={'text-info'}
//                                     />
//                                 </div>
//                                 <div className="col-md-3 mb-3">

//                                     <ExecutiveCard
//                                         title={'High Risk Area'}
//                                         value={'Production'}
//                                         color={'#fffaf0'}
//                                         Icon={FaLocationDot}
//                                         textColor={'text-warning'}
//                                     />
//                                 </div>
//                             </div>
//                         </div>
//                     </div>


//                     <div className="mb-3">
//                         <h5 className="mb-0"> <span className='fw-bold text-primary'>|</span> Time-Based Alert Analysis</h5>
//                     </div>
//                     <div className="card mb-4">
//                         <div className="card-body">
//                             <h6>Alert Distribution by Hour Across Shifts</h6>
//                             <p className="text-muted small">Showing hourly alert patterns across different areas and shifts</p>
//                             <ResponsiveContainer width="100%" height={300}>
//                                 <LineChart data={timeBasedData}>
//                                     <CartesianGrid strokeDasharray="3 3" />
//                                     <XAxis dataKey="time" />
//                                     <YAxis />
//                                     <Tooltip />
//                                     <Legend />
//                                     <Line type="monotone" dataKey="Shift A (Peak at Hour 6-8 alert)" stroke="#8884d8" strokeWidth={2} />
//                                     <Line type="monotone" dataKey="Area 2" stroke="#82ca9d" strokeWidth={2} />
//                                     <Line type="monotone" dataKey="Area 3" stroke="#ffc658" strokeWidth={2} />
//                                 </LineChart>
//                             </ResponsiveContainer>
//                             <div className="text-center mt-2">
//                                 <small className="text-muted">
//                                     <span className="me-3"><span style={{ color: '#8884d8' }}>■</span> Shift A (Peak at Hour 6-8 alert)</span>
//                                 </small>
//                             </div>
//                         </div>
//                     </div>

//                     {/* Area-Based Alert Analysis */}
//                     <div className="mb-3">
//                         <h5 className="mb-0"> <span className='fw-bold text-primary'>|</span> Area-Based Alert Analysis</h5>
//                     </div>
//                     <div className="card mb-4">

//                         <div className="card-body">
//                             <h6>Alert Distribution by Work Area</h6>
//                             <p className="text-muted small">Distribution of safety alerts across different operational areas</p>
//                             <ResponsiveContainer width="100%" height={300}>
//                                 <BarChart data={areaBasedData}>
//                                     <CartesianGrid strokeDasharray="3 3" />
//                                     <XAxis dataKey="area" angle={-45} textAnchor="end" height={100} />
//                                     <YAxis />
//                                     <Tooltip />
//                                     <Bar dataKey="alerts" fill="#007bff" />
//                                 </BarChart>
//                             </ResponsiveContainer>

//                             <div className="mt-3 p-3 bg-light rounded text-dark">
//                                 <h6 className=''>Key Findings:</h6>
//                                 <ul className="mb-0 small">
//                                     <li>Production Floor has the highest alert count (25 alerts)</li>
//                                     <li>Warehouse and Loading dock areas show moderate risk levels</li>
//                                     <li>Focused risk areas have been identified for immediate action</li>
//                                 </ul>
//                             </div>
//                         </div>
//                     </div>

//                     {/* Module-Based Alert Analysis */}
//                     <div className="card mb-4">
//                         <div className="card-header bg-light">
//                             <h6 className="mb-0">🔧 Module-Based Alert Analysis</h6>
//                         </div>
//                         <div className="card-body">
//                             <div className="row">
//                                 <div className="col-md-8">
//                                     <h6>Alert Distribution by Safety Module</h6>
//                                     <p className="text-muted small">Breakdown by equipment and safety system type</p>
//                                     <ResponsiveContainer width="100%" height={300}>
//                                         <PieChart>
//                                             <Pie
//                                                 data={pieData}
//                                                 cx="50%"
//                                                 cy="50%"
//                                                 outerRadius={80}
//                                                 fill="#8884d8"
//                                                 dataKey="value"
//                                                 label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
//                                             >
//                                                 {pieData.map((entry, index) => (
//                                                     <Cell key={`cell-${index}`} fill={entry.color} />
//                                                 ))}
//                                             </Pie>
//                                             <Tooltip />
//                                         </PieChart>
//                                     </ResponsiveContainer>
//                                 </div>
//                                 <div className="col-md-4">
//                                     <h6>Module Details</h6>
//                                     <p className="text-muted small">Current operational status of safety modules</p>
//                                     <div className="mt-4">
//                                         {moduleDetails.map((module, index) => (
//                                             <div key={index} className="d-flex justify-content-between align-items-center p-3 mb-2 border rounded">
//                                                 <div className="d-flex align-items-center">
//                                                     <div
//                                                         className="rounded-circle me-3"
//                                                         style={{
//                                                             width: '12px',
//                                                             height: '12px',
//                                                             backgroundColor: module.color
//                                                         }}
//                                                     ></div>
//                                                     <div>
//                                                         <strong>{module.name}</strong>
//                                                         <br />
//                                                         <small className="text-muted">{module.count}</small>
//                                                     </div>
//                                                 </div>
//                                                 <span className={`badge ${module.status === 'Operational' ? 'bg-success' : 'bg-warning'} text-white`}>
//                                                     {module.status}
//                                                 </span>
//                                             </div>
//                                         ))}
//                                     </div>
//                                 </div>
//                             </div>
//                         </div>
//                     </div>

//                     {/* Compliance Analysis */}
//                     <div className="card mb-4">
//                         <div className="card-header bg-light">
//                             <h6 className="mb-0">✅ Compliance Analysis by Sub-Area</h6>
//                         </div>
//                         <div className="card-body">
//                             <h6>Sub-Area Compliance Metrics</h6>
//                             <p className="text-muted small">Compliance analysis and zone performance by sub-operational sub-area</p>

//                             <div className="row mt-4">
//                                 {/* Zone A1 */}
//                                 <div className="col-md-4 mb-4">
//                                     <div className="card h-100" style={{ backgroundColor: '#e8f5e8' }}>
//                                         <div className="card-header bg-success text-white text-center">
//                                             <strong>Zone A1</strong>
//                                             <div className="small">Excellent</div>
//                                         </div>
//                                         <div className="card-body text-center">
//                                             <div className="mb-3">
//                                                 <strong>Compliance Rate</strong>
//                                                 <div className="display-6 fw-bold text-success">95%</div>
//                                             </div>
//                                             <div>
//                                                 <strong>Total Alerts</strong>
//                                                 <div className="display-6 fw-bold">8</div>
//                                             </div>
//                                         </div>
//                                     </div>
//                                 </div>

//                                 {/* Zone A2 */}
//                                 <div className="col-md-4 mb-4">
//                                     <div className="card h-100" style={{ backgroundColor: '#fff8e1' }}>
//                                         <div className="card-header bg-warning text-dark text-center">
//                                             <strong>Zone A2</strong>
//                                             <div className="small">Good</div>
//                                         </div>
//                                         <div className="card-body text-center">
//                                             <div className="mb-3">
//                                                 <strong>Compliance Rate</strong>
//                                                 <div className="display-6 fw-bold text-warning">87%</div>
//                                             </div>
//                                             <div>
//                                                 <strong>Total Alerts</strong>
//                                                 <div className="display-6 fw-bold">15</div>
//                                             </div>
//                                         </div>
//                                     </div>
//                                 </div>

//                                 {/* Zone B1 */}
//                                 <div className="col-md-4 mb-4">
//                                     <div className="card h-100" style={{ backgroundColor: '#e8f5e8' }}>
//                                         <div className="card-header bg-success text-white text-center">
//                                             <strong>Zone B1</strong>
//                                             <div className="small">Excellent</div>
//                                         </div>
//                                         <div className="card-body text-center">
//                                             <div className="mb-3">
//                                                 <strong>Compliance Rate</strong>
//                                                 <div className="display-6 fw-bold text-success">92%</div>
//                                             </div>
//                                             <div>
//                                                 <strong>Total Alerts</strong>
//                                                 <div className="display-6 fw-bold">12</div>
//                                             </div>
//                                         </div>
//                                     </div>
//                                 </div>
//                             </div>
//                         </div>
//                     </div>

//                     {/* Footer */}
//                     <div className="text-center mt-5 pt-4 border-top">
//                         <p className="text-muted small">
//                             This report is automatically generated daily at 7:00 AM, 3:00 PM, and 11:00 PM
//                             <br />
//                             For questions or support, contact the safety <NAME_EMAIL> or call the hotline
//                         </p>
//                     </div>
//                 </div>
//             </div>
//         </div>
//     );
// };

// export default TestComponent;


// function ExecutiveCard(props) {
//     const { title, value, color, Icon, textColor } = props;
//     return (
//         <>
//             <div className="p-3 border rounded" style={{ backgroundColor: color }}>
//                 <div className={`${textColor} mb-2`}>
//                     <Icon size={30} />
//                 </div>
//                 <div className={`display-7 fw-bold ${textColor}`}>{value}</div>
//                 <small className="text-muted">{title}</small>
//             </div>
//         </>
//     )
// }






















import React from 'react';
import Chart from 'react-apexcharts';
import { IoWarningOutline } from "react-icons/io5";
import { FaArrowTrendUp, FaLocationDot } from "react-icons/fa6";
import { MdOutlineWatchLater } from "react-icons/md";
import { getCurrentDate, getCurrentTime } from '../utils/getCurrentTime';

const TestComponent = ({ heatmapData, subAreas, executiveSummary, filters, progressData, alertsForReports }) => {

    const areaBasedData1 = heatmapData?.areaOwner?.map((owner, index) => {
        let alertCount = 0;

        for (const item of heatmapData.data) {
            const value = item.data[index];
            if (value !== -1) {
                alertCount += value;
            }
        }

        return { area: owner, alerts: alertCount };
    });

    console.log("areaBasedData1", areaBasedData1);
    // Time-based Alert Analysis Data - Modified for single line chart

    const timeBasedAlerts = alertsForReports?.y_axis
    // [2, 3, 4, 6, 8, 10, 8, 6, 4, 2];
    const timeLabels = alertsForReports?.x_axis;
    // const timeLabels = ['06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00', '24:00'];
    const neww = timeBasedAlerts?.length > 0 ? timeBasedAlerts : [0]
    // Line Chart Options for Time-Based Alert Analysis
    const dataMax = Math?.max(...neww);
    const lineChartOptions = {
        chart: {
            type: 'line',
            height: 300,
            toolbar: {
                show: false
            }
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        colors: ['#8884d8'],
        xaxis: {
            categories: timeLabels,
            title: {
                text: 'Time'
            }

        },
        yaxis: {
            title: {
                text: 'Number of Alerts'
            },
            max: dataMax ? dataMax + 1 : 2,
            tickAmount: (dataMax + 1) > 5 ? 5 : dataMax + 1,
            forceNiceScale: true
        },
        grid: {
            strokeDashArray: 3
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " alerts"
                }
            }
        },
        legend: {
            show: true,
            labels: {
                colors: ['#333']
            }
        },
        annotations: {
            points: [{
                x: '16:00',
                y: 10,
                marker: {
                    size: 8,
                    fillColor: '#dc3545',
                    strokeColor: '#fff',
                    strokeWidth: 2
                },
                label: {
                    text: 'Peak Hour',
                    style: {
                        color: '#fff',
                        background: '#dc3545'
                    }
                }
            }]
        }
    };

    const lineChartSeries = [{
        name: 'Shift A (Peak at Hour 6-8 alert)',
        data: timeBasedAlerts
    }];

    // Area-based Alert Analysis Data
    const areaBasedData = [
        { area: 'Production Floor', alerts: 25 },
        { area: 'Warehouse', alerts: 15 },
        { area: 'Loading Dock', alerts: 12 },
        { area: 'Quality Control', alerts: 8 },
        { area: 'Maintenance', alerts: 6 },
        { area: 'Office', alerts: 3 },
        { area: 'Cafeteria', alerts: 2 }
    ];

    // Bar Chart Options
    const barChartOptions = {
        chart: {
            type: 'bar',
            height: 300,
            toolbar: {
                show: false
            }
        },
        colors: ['#007bff'],
        xaxis: {
            categories: areaBasedData1?.map(item => item?.area),
            labels: {
                rotate: -45
            },
            title: {
                text: 'Areas'
            }
        },
        yaxis: {
            title: {
                text: 'Number of Alerts'
            },
            forceNiceScale: true
        },
        grid: {
            strokeDashArray: 3
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " alerts"
                }
            }
        }
    };

    const barChartSeries = [{
        name: 'Alerts',
        data: areaBasedData1?.map(item => item?.alerts)
    }];
    // console.log('barChartSeriesbarChartSeries', barChartSeries[0]?.data?.length > 0 ? Math?.max(...barChartSeries[0]?.data) : 0);
  const barchartSeriesValue = barChartSeries[0]?.data?.length > 0 ? Math?.max(...barChartSeries[0]?.data) : 0;

    // Function to get module color based on name
    const getModuleColor = (moduleName) => {
        const colorMap = {
            'Helmet': '#8884D8',
            'Vest': '#82CA9D',
            'MMHE': '#FFC658',
            'Emergency Exit': '#FF7C7C',
            'No Go': '#2196f3'
        };
        return colorMap[moduleName] || '#6f42c1'; // Default color if module not found
    };

    // Pie Chart Data
    const newProgressData = progressData?.progressData?.filter((item) => item?.alerts >= 0)
    const pieData = newProgressData?.map((item) => {
        return { name: item?.name, value: item?.alerts, color: getModuleColor(item?.name) }
    })

    // [
    //     { name: 'Emergency Exit', value: 35, color: '#dc3545' },
    //     { name: 'Helmet', value: 25, color: '#17a2b8' },
    //     { name: 'MMHE', value: 20, color: '#ffc107' },
    //     { name: 'Vest', value: 15, color: '#28a745' },
    // ];

    // Pie Chart Options
    const pieChartOptions = {
        chart: {
            type: 'pie',
            height: 300
        },
        labels: pieData?.map(item => item?.name),
        colors: pieData?.map(item => item?.color),
        legend: {
            position: 'bottom'
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + "%"
                }
            }
        }
    };

    const pieChartSeries = pieData?.map(item => item?.value);

    // Function to generate dynamic key findings based on actual data
    const generateKeyFindings = (data) => {
        if (!data || data.length === 0) {
            return <li>No data available for analysis</li>;
        }

        const findings = [];

        // Sort areas by alert count to find highest and lowest
        const sortedData = [...data].sort((a, b) => b.alerts - a.alerts);
        const totalAlerts = data.reduce((sum, item) => sum + item.alerts, 0);

        // Finding 1: Highest alert area
        if (sortedData.length > 0 && sortedData[0].alerts > 0) {
            findings.push(
                <li key="highest">{sortedData[0].area} has the highest alert count ({sortedData[0].alerts} alerts)</li>
            );
        }

        // Finding 2: Areas with moderate risk (middle range)
        const moderateRiskAreas = sortedData.filter((item, index) =>
            index > 0 && index < sortedData.length - 1 && item.alerts > 0
        ).slice(0, 2); // Take top 2 moderate risk areas

        if (moderateRiskAreas.length > 0) {
            const areaNames = moderateRiskAreas.map(area => area.area).join(' and ');
            findings.push(
                <li key="moderate">{areaNames} show moderate risk levels</li>
            );
        }

        // Finding 3: Total alerts summary
        if (totalAlerts > 0) {
            const highRiskCount = sortedData.filter(item => item.alerts > (totalAlerts / data.length)).length;
            findings.push(
                <li key="summary">
                    {highRiskCount} area{highRiskCount !== 1 ? 's' : ''} identified for immediate attention with above-average alert levels
                </li>
            );
        }

        // Finding 4: Areas with no alerts (if any)
        const noAlertAreas = sortedData.filter(item => item.alerts === 0);
        if (noAlertAreas.length > 0 && noAlertAreas.length < data.length) {
            findings.push(
                <li key="no-alerts">
                    {noAlertAreas.length} area{noAlertAreas.length !== 1 ? 's' : ''} maintained zero alerts during this period
                </li>
            );
        }

        return findings.length > 0 ? findings : <li>All areas show normal activity levels</li>;
    };

    // Module Details Data
    console.log('progressDataprogressData', progressData)
    console.log('newProgressDatanewProgressData', newProgressData)
    const moduleDetails = newProgressData?.map((item) => {
        const total = Number(progressData?.totalAlerts) || 0;
        const count = Number(item?.alerts) || 0;
        let percent = 0;
        if (total > 0) {
            const rawPercent = (count / total) * 100;
            percent = rawPercent % 1 === 0 ? rawPercent.toFixed(0) : rawPercent.toFixed(1);
        }
        return { name: item?.name, percent, count: item?.alerts, color: getModuleColor(item?.name) }

    })
    console.log('moduleDetailsmoduleDetails', moduleDetails)
    console.log('moduleDetailsmoduleDetails', moduleDetails)
    //  progressData?.map((item)=> {
    //     let percentage = ((item?.alert/progressData?.totalAlerts)*100)?.toFixed(1)
    //     return [
    //         { name: item?.name, percent: percentage, count: item?.alert, color: '#6f42c1' }
    //     ]
    // })

    // [
    //     { name: 'Helmet', percent: '12', count: '12', color: '#6f42c1' },
    //     { name: 'Vest', percent: '13', count: '45', color: '#28a745' },
    //     { name: 'Forklift', percent: '45', count: '35', color: '#ffc107' },
    //     { name: 'Emergency Exit', percent: '44', count: '50', color: '#dc3545' }
    // ];

    return (
        <div className='d-flex justify-content-center'>
            <div style={{ maxWidth: '1000px' }}>
                {/* Bootstrap CSS */}
                {/* <link
                    href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css"
                    rel="stylesheet"
                /> */}

                <div className="container-fluid p-4" style={{ backgroundColor: '#f8f9fa', minHeight: '100vh', padding: '0 60px' }}>
                    {/* Header */}
                    <div className="text-center mb-4">
                        <h2 className="fw-bold">{`Safety ${filters?.shifts?.length > 0 ? filters?.shifts[0] : 'Shift A'} Report`}</h2>
                        <p className="text-muted">Comprehensive Analysis of Safety Alerts and Compliance Metrics</p>
                        <div className="d-flex justify-content-center gap-4 text-muted small">
                            <span>📅 Report Date: {(filters?.starting && filters?.ending) ? `${filters?.starting} To ${filters?.ending} ` : filters?.week || filters?.month || filters?.date}</span>
                            <span>⏰ Reporting Period: {filters?.shifts?.length > 0 ? filters?.shifts[0] : 'Shift A'}</span>
                            <span>📊 Generated: {getCurrentTime()}</span>
                        </div>
                    </div>
                    <div>
                        <hr />
                    </div>

                    {/* Executive Summary */}
                    <div className=" mb-4 border-none">
                        <div className="mb-3">
                            <h5 className="mb-0"> <span className='fw-bold text-primary'>|</span> Executive Summary</h5>
                        </div>
                        <div className="">
                            <div className="row text-center">
                                <div className="col-md-3 mb-3">
                                    <ExecutiveCard
                                        title={'Total Alerts'}
                                        value={executiveSummary?.total_alerts || 'N/A'}
                                        color={'#FEF2F2'}
                                        Icon={IoWarningOutline}
                                        textColor={'#B91C1C'}
                                    />
                                </div>
                                <div className="col-md-3 mb-3">
                                    <ExecutiveCard
                                        title={'Site Compliance'}
                                        value={executiveSummary?.site_compliance || 'N/A'}
                                        color={'#BBF7D0'}
                                        Icon={FaArrowTrendUp}
                                        textColor={'#15803D'}
                                    />
                                </div>
                                <div className="col-md-3 mb-3">

                                    <ExecutiveCard
                                        title={'Peak Alert Time'}
                                        value={executiveSummary?.peak_alert_time || 'N/A'}
                                        color={'#BFDBFE'}
                                        Icon={MdOutlineWatchLater}
                                        textColor={'#1D4ED8'}
                                    />
                                </div>
                                <div className="col-md-3 mb-3">

                                    <ExecutiveCard
                                        title={'High Risk Area'}
                                        value={executiveSummary?.high_risk_area || 'N/A'}
                                        color={'#E9D5FF'}
                                        Icon={FaLocationDot}
                                        textColor={'#7E22CE'}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="mb-3">
                        <h5 className="mb-0"> <span className='fw-bold text-primary'>|</span> Time-Based Alert Analysis</h5>
                    </div>
                    <div className="card mb-4">
                        <div className="card-body">
                            <h6>Alert Distribution by Hour Across Shifts</h6>
                            <p className="text-muted small">Showing hourly alert patterns across different areas and shifts</p>
                            {Math?.max(...neww) > 0 ? <><Chart
                                options={lineChartOptions}
                                series={lineChartSeries}
                                type="line"
                                height={300}
                            />
                                <div className="text-center mt-2">
                                    <small className="text-muted">
                                        <span className="me-3"><span style={{ color: '#8884d8' }}>■</span> Shift A (Peak alert time: {executiveSummary?.peak_alert_time || 'N/A'})</span>
                                    </small>
                                </div></> : <div className="d-flex justify-content-center p-4"><h6>No Data Available</h6></div>}
                        </div>
                    </div>

                    {/* Area-Based Alert Analysis */}
                    <div className="mb-3">
                        <h5 className="mb-0"> <span className='fw-bold text-primary'>|</span> Area-Based Alert Analysis</h5>
                    </div>
                    <div className="card mb-4">

                        <div className="card-body">
                            <h6>Alert Distribution by Work Area</h6>
                            {barchartSeriesValue > 0 ?
                             <><p className="text-muted small">Distribution of safety alerts across different operational areas</p>
                                {(heatmapData && Object.keys(heatmapData).length === 0) ? <div className="d-flex justify-content-center p-4"><h6>No Data Available</h6></div> : !heatmapData ? <div className="d-flex justify-content-center p-4"><h6>No Data Available</h6></div> : <Chart
                                    options={barChartOptions}
                                    series={barChartSeries}
                                    type="bar"
                                    height={300}
                                />}

                                <div className="mt-3 p-3 bg-light rounded text-dark">
                                    <h6 className=''>Key Findings:</h6>
                                    <ul className="mb-0 small">
                                        {generateKeyFindings(areaBasedData1)}
                                    </ul>
                                </div></> : <div className="d-flex justify-content-center p-4"><h6>No Data Available</h6></div>}
                        </div>
                    </div>

                    {/* Module-Based Alert Analysis */}
                    <div className="mb-3">
                        <h5 className="mb-0"> <span className='fw-bold text-primary'>|</span> Module-Based Alert Analysis</h5>
                    </div>
                    <div className="card mb-4">
                        <div className="card-body">
                            {progressData?.totalAlerts > 0 && <div className="row">
                                <div className="col-md-7">
                                    <h6>Alert Distribution by Safety Module</h6>
                                    <p className="text-muted small">Breakdown by equipment and safety system type</p>   
                                    <Chart
                                        options={pieChartOptions}
                                        series={pieData?.map(item => item?.value)}
                                        type="pie"
                                        height={300}
                                    />
                                </div>
                                <div className="col-md-5">
                                    <h6>Module Details</h6>
                                    <p className="text-muted small">Current operational status of safety modules</p>
                                    <div className="mt-4">
                                        {moduleDetails?.map((module, index) => (
                                            <div key={index} className="d-flex justify-content-between align-items-center p-3 mb-2 border rounded">
                                                <div className="d-flex align-items-center">
                                                    <div
                                                        className="rounded-circle me-3"
                                                        style={{
                                                            width: '20px',
                                                            height: '20px',
                                                            backgroundColor: module.color
                                                        }}
                                                    ></div>
                                                    <div>
                                                        <strong>{module.name}</strong>
                                                        <br />
                                                        <small className="text-muted">{module.count} Alerts</small>
                                                    </div>
                                                </div>
                                                <span style={{backgroundColor:module.color}} className={`badge text-black`}>
                                                    {module.percent}%
                                                </span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>}
                            {progressData?.totalAlerts === 0 ? <div className="d-flex justify-content-center p-4"><h6>No Data Available</h6></div> : null}
                        </div>
                    </div>

                    {/* Compliance Analysis */}

                    <div className="mb-3">
                        <h5 className="mb-0"> <span className='fw-bold text-primary'>|</span> Compliance Analysis by Sub-Area</h5>
                    </div>
                    <div className="card mb-4">

                        <div className="card-body">
                            <h6>Sub-Area Compliance Metrics</h6>
                            <p className="text-muted small">Compliance percentages and alert summaries for each operational sub-area</p>

                            <div className="row mt-4">
                                {/* Zone A1 */}
                                {

                                    subAreas?.map((item, index) => {
                                        const badge = getComplianceLabel(item?.compliance)
                                        return (<div key={index} className="col-md-4 mb-4">
                                            <div className="card" style={{ backgroundColor: '#e8f5e8' }}>
                                                <div className={`card-header ${badge.color === 'success' ? 'bg-success' : badge.color === 'warning' ? 'bg-warning' : 'bg-danger'} text-white text-center d-flex justify-content-between`}>
                                                    <strong>{item?.subareaName}</strong>
                                                    <div className={`px-2 py-1 rounded-pill border d-flex justify-content-center align-items-center text-white`} style={{ borderColor: '#fff', width: 'min-content', height: 'max-content' }}>
                                                        <strong>{badge.text}</strong>
                                                    </div>
                                                </div>
                                                <div className="card-body text-center">
                                                    <div className="mb-2 d-flex justify-content-between align-items-center">
                                                        <strong>Compliance Rate</strong>
                                                        <div className={`display-6 fw-bold text-${badge.color}`}>{item?.compliance}%</div>
                                                    </div>
                                                    <div className="progress bg-dark" role="progressbar" aria-label="Basic example" aria-valuenow={Number(item?.compliance)} aria-valuemin="0" aria-valuemax="100">
                                                        <div className={`progress-bar bg-${badge.color}`} style={{ width: `${item?.compliance}%` }}></div>
                                                    </div>
                                                    <div><hr className='mb-0' /></div>
                                                    <div className='d-flex justify-content-between align-items-center'>
                                                        <strong>Total Alerts</strong>
                                                        <div className="display-6 fw-bold">{item?.alerts}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>)
                                    })
                                }
                                {
                                    subAreas?.length === 0 ? <div className="d-flex justify-content-center p-4"><h6>No Data Available</h6></div> : null
                                }
                                <div>
                                    {/* Compliance Summary Section */}
                                    <div className="card mb-4 mt-4">
                                        <div className="card-body">
                                            <h6 className="mb-3">Compliance Summary</h6>

                                            {(() => {
                                                // Calculate compliance categories
                                                const excellent = subAreas?.filter(area => Number(area?.compliance) === 100)?.length;
                                                const good = subAreas?.filter(area => Number(area?.compliance) >= 95 && Number(area?.compliance) < 100)?.length;
                                                const needsImprovement = subAreas?.filter(area => Number(area?.compliance) < 95)?.length;

                                                // Calculate percentages
                                                const total = subAreas?.length;
                                                const excellentPercent = Math?.round((excellent / total) * 100);
                                                const goodPercent = Math?.round((good / total) * 100);
                                                const needsImprovementPercent = Math?.round((needsImprovement / total) * 100);

                                                return (
                                                    <>
                                                        <div className="row text-center mb-3">
                                                            <div className="col-md-4">
                                                                <div className="p-3 rounded" style={{ backgroundColor: '#e8f5e8' }}>
                                                                    <h2 className="display-4 fw-bold text-success mb-0">{excellent}</h2>
                                                                    <p className="mb-0">Excellent</p>
                                                                    <small className="text-muted">100% Compliance</small>
                                                                </div>
                                                            </div>
                                                            <div className="col-md-4">
                                                                <div className="p-3 rounded" style={{ backgroundColor: '#fff8e1' }}>
                                                                    <h2 className="display-4 fw-bold text-warning mb-0">{good}</h2>
                                                                    <p className="mb-0">Good</p>
                                                                    <small className="text-muted">95-99% Compliance</small>
                                                                </div>
                                                            </div>
                                                            <div className="col-md-4">
                                                                <div className="p-3 rounded" style={{ backgroundColor: '#ffebee' }}>
                                                                    <h2 className="display-4 fw-bold text-danger mb-0">{needsImprovement}</h2>
                                                                    <p className="mb-0">Needs Improvement</p>
                                                                    <small className="text-muted">Below 95% Compliance</small>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div className="progress" style={{ height: '30px' }}>
                                                            {excellent > 0 && (
                                                                <div
                                                                    className="progress-bar bg-success"
                                                                    role="progressbar"
                                                                    style={{ width: `${excellentPercent}%` }}
                                                                    aria-valuenow={excellentPercent}
                                                                    aria-valuemin="0"
                                                                    aria-valuemax="100"
                                                                >
                                                                    {excellent} ({excellentPercent}%)
                                                                </div>
                                                            )}
                                                            {good > 0 && (
                                                                <div
                                                                    className="progress-bar bg-warning"
                                                                    role="progressbar"
                                                                    style={{ width: `${goodPercent}%` }}
                                                                    aria-valuenow={goodPercent}
                                                                    aria-valuemin="0"
                                                                    aria-valuemax="100"
                                                                >
                                                                    {good} ({goodPercent}%)
                                                                </div>
                                                            )}
                                                            {needsImprovement > 0 && (
                                                                <div
                                                                    className="progress-bar bg-danger"
                                                                    role="progressbar"
                                                                    style={{ width: `${needsImprovementPercent}%` }}
                                                                    aria-valuenow={needsImprovementPercent}
                                                                    aria-valuemin="0"
                                                                    aria-valuemax="100"
                                                                >
                                                                    {needsImprovement} ({needsImprovementPercent}%)
                                                                </div>
                                                            )}
                                                        </div>

                                                        <div className="row mt-4">
                                                            <div className="col-md-6">
                                                                <div className="card">
                                                                    <div className="card-body">
                                                                        <h6 className="card-title">Key Insights</h6>
                                                                        <ul className="mb-0">
                                                                            <li>{Math.round((excellent / total) * 100)}% of areas have perfect compliance (100%)</li>
                                                                            <li>{Math.round(((excellent + good) / total) * 100)}% of areas have good or excellent compliance (≥95%)</li>
                                                                            {needsImprovement > 0 && (
                                                                                <li>{needsImprovement} area{needsImprovement > 1 ? 's' : ''} require{needsImprovement === 1 ? 's' : ''} immediate attention</li>
                                                                            )}
                                                                            <li>Total alerts across all areas: {subAreas?.reduce((sum, area) => sum + Number(area?.alerts), 0)}</li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </>
                                                );
                                            })()}
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="text-center mt-5 pt-4 border-top">
                        <p className="text-muted small">
                            This report is generated at {getCurrentDate()}, {getCurrentTime()}
                            <br />
                            For questions or support, contact the safety <NAME_EMAIL> or call the hotline
                        </p>
                    </div>
                </div>
            </div> 
        </div>
    );
};

export default TestComponent;

function ExecutiveCard(props) {
    const { title, value, color, Icon, textColor } = props;
    const borderColor = hexToRgba(textColor, 0.25)
    return (
        <>
            <div className="p-3 rounded" style={{ backgroundColor: color, color: textColor, border: `1px solid ${borderColor}` }}>
                <div className={`mb-2`}>
                    <Icon size={30} />
                </div>
                <div className={`fs-3 fw-bold `}>{value}</div>
                <small className="">{title}</small>
            </div>
        </>
    )
}

function hexToRgba(hex, alpha = 0.3) {
  const [r, g, b] = hex
    .replace('#','')
    .match(/.{2}/g)
    .map(v => parseInt(v, 16));
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}


const getComplianceLabel = (compliance) => {
    const value = Number(compliance);
    if (value == 100) return { text: "Excellent", color: "success" };
    if (value >= 95 && value < 100) return { text: "Good", color: "warning" };
    return { text: "Needs Improvement", color: "danger" };
};