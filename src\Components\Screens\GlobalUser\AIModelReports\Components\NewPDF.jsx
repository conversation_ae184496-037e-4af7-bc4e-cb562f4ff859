

import { Download, AlertTriangle, TrendingUp, Clock, MapPin } from "lucide-react";
import Chart from "react-apexcharts";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { IoWarningOutline } from "react-icons/io5";
import { FaArrowTrendUp } from "react-icons/fa6";
import { MdOutlineWatchLater } from "react-icons/md";
import { BiMap } from "react-icons/bi";
import { useEffect, useState } from "react";
import AreaService from "../../../../../api/areaService";
import { executive_summary_for_report, get_alerts_for_report } from "../../../../../RenderPDFformat/utils/reportApiUtils";

const DailySafetyReport = ({ filters, isPdfRendering, apiData }) => {

 const timeBasedAlerts = apiData?.alertsChartsData.y_axis
 console.log('show me apiData pdf', apiData)
    // [2, 3, 4, 6, 8, 10, 8, 6, 4, 2];
    const timeLabels = apiData?.alertsChartsData.x_axis
    // const timeLabels = ['06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00', '24:00'];
    const neww = timeBasedAlerts?.length > 0 ? timeBasedAlerts : [0]
    // Line Chart Options for Time-Based Alert Analysis
    const dataMax = Math?.max(...neww);
    const lineChartOptions = {
        chart: {
            type: 'line',
            height: 300,
            toolbar: {
                show: false
            }
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        colors: ['#8884d8'],
        xaxis: {
            categories: timeLabels,
            title: {
                text: 'Time'
            }

        },
        yaxis: {
            title: {
                text: 'Number of Alerts'
            },
            max: dataMax ? dataMax + 1 : 2,
            tickAmount: (dataMax + 1) > 5 ? 5 : dataMax + 1,
            forceNiceScale: true
        },
        grid: {
            strokeDashArray: 3
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " alerts"
                }
            }
        },
        legend: {
            show: true,
            labels: {
                colors: ['#333']
            }
        },
        annotations: {
            points: [{
                x: '16:00',
                y: 10,
                marker: {
                    size: 8,
                    fillColor: '#dc3545',
                    strokeColor: '#fff',
                    strokeWidth: 2
                },
                label: {
                    text: 'Peak Hour',
                    style: {
                        color: '#fff',
                        background: '#dc3545'
                    }
                }
            }]
        }
    };

    const lineChartSeries = [{
        name: 'Shift A (Peak at Hour 6-8 alert)',
        data: timeBasedAlerts
    }];
  const [firstseries, setfirstseries] = useState([
    {
      name: "Day Shift",
      data: [2, 4, 6, 4, 5, 6, 3, 5, 6, 4, 3, 2, 5, 6, 3, 4, 4, 2, 3, 5, 4, 2, 3, 2],
    },
    {
      name: "Night Shift",
      data: [1, 2, 3, 2, 3, 4, 2, 3, 4, 2, 2, 1, 3, 4, 2, 2, 2, 1, 2, 3, 2, 1, 2, 1],
    },
    {
      name: "Weekend",
      data: [0, 1, 2, 1, 1, 2, 1, 2, 2, 1, 1, 0, 2, 2, 1, 1, 1, 0, 1, 2, 1, 0, 1, 0],
    },
  ])



  const shiftChartOptions = {
    chart: {
      // id: "alerts",
      toolbar: { show: false },
      background: "transparent",
    },
    xaxis: {
      categories: [
        "6:00", "7:00", "8:00", "9:00", "10:00", "11:00",
        "12:00", "13:00", "14:00", "15:00", "16:00", "17:00",
        "18:00", "19:00", "20:00", "21:00", "22:00", "23:00",
        "0:00", "1:00", "2:00", "3:00", "4:00", "5:00"
      ],
      labels: {
        style: {
          fontSize: "10px",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "10px",
        },
      },
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    colors: ["#8b5cf6"],
    grid: {
      show: true,
      strokeDashArray: 3,
    },
  };

  const shiftChartSeries = [
    {
      name: "Day Shift",
      data: [2, 4, 6, 4, 5, 6, 3, 5, 6, 4, 3, 2, 5, 6, 3, 4, 4, 2, 3, 5, 4, 2, 3, 2],
    },
    {
      name: "Night Shift",
      data: [1, 2, 3, 2, 3, 4, 2, 3, 4, 2, 2, 1, 3, 4, 2, 2, 2, 1, 2, 3, 2, 1, 2, 1],
    },
    {
      name: "Weekend",
      data: [0, 1, 2, 1, 1, 2, 1, 2, 2, 1, 1, 0, 2, 2, 1, 1, 1, 0, 1, 2, 1, 0, 1, 0],
    },
  ];

  const pyramidData = [
    { level: "Fatality/Class A", color: "#7f1d1d", fy2024: 0, ytd2024: 0, ytd2025: 0 },
    { level: "LTA", color: "#991b1b", fy2024: 1, ytd2024: 1, ytd2025: 0 },
    { level: "RWC", color: "#b91c1c", fy2024: 0, ytd2024: 0, ytd2025: 0 },
    { level: "MTC", color: "#ea580c", fy2024: 0, ytd2024: 0, ytd2025: 0 },
    { level: "FAC", color: "#f97316", fy2024: 15, ytd2024: 3, ytd2025: 5 },
    { level: "Near Miss", color: "#fb923c", fy2024: 144, ytd2024: 57, ytd2025: 68 },
    { level: "Hazards", color: "#eab308", fy2024: 10065, ytd2024: 4352, ytd2025: 4217 },
    { level: "SBO", color: "#facc15", fy2024: 3346, ytd2024: 896, ytd2025: 1383 },

  ];


  const totalAlertsData = {
    categories: [
      "Production Floor",
      "Warehouse",
      "Loading Dock",
      "Office Area",
      "Maintenance Shop",
      "Storage Area",
    ],
    series: [31, 21, 19, 8, 27, 15], // approximate from image
  };
  const alertsByShiftData = {
    categories: [
      "Production Floor",
      "Warehouse",
      "Loading Dock",
      "Office Area",
      "Maintenance Shop",
      "Storage Area",
    ],
    series: [
      {
        name: "Shift A",
        data: [1.5, 1, 1, 0.2, 1, 0.3],
      },
      {
        name: "Shift B",
        data: [1, 0.6, 1.5, 0.1, 1.1, 0.4],
      },
      {
        name: "Shift C",
        data: [0.5, 0.4, 0.2, 0.2, 0.7, 0.3],
      },
    ],
  };
  // Chart config for Total Alerts by Area
  const areaBarOptions = {
    chart: {
      type: "bar",
      toolbar: { show: false },
      background: "transparent",
    },
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: "40%",
      },
    },
    xaxis: {
      categories: totalAlertsData.categories,
      labels: {
        style: { fontSize: "12px" },
      },
    },
    yaxis: {
      labels: {
        style: { fontSize: "12px" },
      },
    },
    colors: ["#8b5cf6"],
  };

  const areaBarSeries = [
    {
      name: "Total Alerts",
      data: totalAlertsData.series,
    },
  ];

  // Chart config for Alerts by Area and Shift
  const areaShiftOptions = {
    chart: {
      type: "bar",
      stacked: true,
      toolbar: { show: false },
      background: "transparent",
    },
    xaxis: {
      categories: alertsByShiftData.categories,
      labels: {
        style: { fontSize: "12px" },
      },
    },
    yaxis: {
      labels: {
        style: { fontSize: "12px" },
      },
    },
    colors: ["#8b5cf6", "#10b981", "#f59e0b"],
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "50%",
        borderRadius: 4,
      },
    },
    legend: {
      position: "top",
    },
  };

  const areaShiftSeries = alertsByShiftData.series;



  const modulePieData = {
    labels: ["Helmet", "Vest", "Emergency Exit", "MMHE"],
    series: [30.9, 20.5, 47.6, 0.9],
  };
  const shiftPieSeries = [33.3, 33.3, 33.4]; // Equal distribution across A, B, C
  const modulePieOptions = {
    chart: {
      type: "pie",
      background: "transparent",
    },
    labels: modulePieData.labels,
    legend: {
      position: "right",
      fontSize: "12px",
    },
    dataLabels: {
      enabled: true,
      formatter: (val, opts) => `${opts.w.globals.labels[opts.seriesIndex]}: ${val.toFixed(1)}%`,
    },
    colors: ["#8b5cf6", "#06b6d4", "#f97316", "#84cc16"],
  };

  const shiftPieOptions = {
    chart: {
      type: "pie",
      background: "transparent",
    },
    labels: ["Shift A", "Shift B", "Shift C"],
    legend: {
      show: false,
    },
    dataLabels: {
      enabled: false,
    },
    colors: ["#8b5cf6", "#10b981", "#f59e0b"],
  };

  const shiftPieSeriess = [33.3, 33.3, 33.4];


  const complianceMetrics = [
    { zone: "Zone A1", compliance: 95, alerts: 8, label: "Excellent", color: "#dcfce7" },
    { zone: "Zone A2", compliance: 87, alerts: 15, label: "Good", color: "#e0f2fe" },
    { zone: "Zone B1", compliance: 92, alerts: 12, label: "Excellent", color: "#dcfce7" },
    { zone: "Zone B2", compliance: 78, alerts: 25, label: "Needs Improvement", color: "#fee2e2" },
    { zone: "Zone C1", compliance: 85, alerts: 18, label: "Good", color: "#e0f2fe" },
    { zone: "Zone C2", compliance: 90, alerts: 14, label: "Excellent", color: "#dcfce7" },
  ];

  const subAreaShiftSeries = [
    {
      name: "Shift A",
      data: [2, 3, 3, 4, 4, 3],
    },
    {
      name: "Shift B",
      data: [2, 2, 3, 3, 4, 2],
    },
    {
      name: "Shift C",
      data: [1, 2, 2, 3, 2, 2],
    },
  ];

  const subAreaShiftOptions = {
    chart: {
      type: "bar",
      stacked: true,
      background: "transparent",
      toolbar: { show: false },
    },
    xaxis: {
      categories: ["Zone A1", "Zone A2", "Zone B1", "Zone B2", "Zone C1", "Zone C2"],
      labels: { style: { fontSize: "12px" } },
    },
    yaxis: { labels: { style: { fontSize: "12px" } } },
    colors: ["#8b5cf6", "#10b981", "#f59e0b"],
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "50%",
        borderRadius: 4,
      },
    },
    legend: { position: "top" },
  };
  const shiftAStats = {
    totalAlerts: 74,
    peakHour: "9AM",
    topArea: "Production",
    topModule: "Helmet",
  };

  const nearMissAreaData = {
    categories: [
      "Cold Store", "Mixing", "Production", "Outside Area", "Admin",
      "Outside Factory", "Contractors", "Dry Store", "Palletizing",
      "Electrical", "IT Room", "Biomass"
    ],
    series: [4, 5, 12, 3, 2, 6, 4, 3, 5, 1, 2, 1], // example values
  };
  const nearMissMonthlyData = {
    categories: ["January", "February", "March", "April"],
    series: [16, 22, 18, 21], // example values
  };
  const areaChartOptions = {
    chart: { type: "bar", toolbar: { show: false }, background: "transparent" },
    xaxis: {
      categories: nearMissAreaData.categories,
      labels: {
        rotate: -45,
        style: { fontSize: "10px" },
      },
    },
    yaxis: {
      labels: { style: { fontSize: "10px" } },
    },
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: "40%",
      },
    },
    colors: ["#3b82f6"],
    grid: { padding: { bottom: 10 } },
  };

  const areaChartSeries = [
    { name: "Near Misses", data: nearMissAreaData.series },
  ];

  const monthlyChartOptions = {
    chart: { type: "line", toolbar: { show: false }, background: "transparent" },
    xaxis: {
      categories: nearMissMonthlyData.categories,
      labels: { style: { fontSize: "10px" } },
    },
    yaxis: {
      labels: { style: { fontSize: "10px" } },
    },
    stroke: { curve: "smooth", width: 2 },
    colors: ["#f59e0b"],
    grid: { padding: { bottom: 10 } },
  };

  const monthlyChartSeries = [
    { name: "Monthly Near Misses", data: nearMissMonthlyData.series },
  ];



  const openNearMissesSeries = [
    {
      name: "Open Near Misses",
      data: [3, 4, 2, 1, 3, 2, 3, 4, 1, 2],
    },
  ];
  const openNearMissesOptions = {
    chart: { type: "bar", toolbar: { show: false }, background: "transparent" },
    xaxis: {
      categories: [
        "Widen Admin", "Admin Mechanical", "Admin Electrical", "Fabrics Area",
        "Production", "Maintenance", "Store", "Dry Area", "Utility", "General",
      ],
      labels: { rotate: -45, style: { fontSize: "10px" } },
    },
    yaxis: { labels: { style: { fontSize: "10px" } } },
    colors: ["#3b82f6"],
  };


  const incidentActionSeries = [
    {
      name: "Open Actions",
      data: [2, 3, 1, 2, 1, 2, 1, 1],
    },
  ];
  const incidentActionOptions = {
    chart: { type: "bar", toolbar: { show: false }, background: "transparent" },
    xaxis: {
      categories: [
        "Shift Incharge", "Admin Head", "Operations Head", "Digital Lead",
        "MHE Head", "Safety Officer", "Wider Area", "Waseer Ahmed",
      ],
      labels: { rotate: -45, style: { fontSize: "10px" } },
    },
    yaxis: { labels: { style: { fontSize: "10px" } } },
    colors: ["#22c55e"],
  };


  const actionItems = [
    {
      number: "AC-INC-20230713-0016",
      category: "Incident",
      source: "Arc Flash - VFD Panel",
      description: "Evaluate Switchgear Control Logic...",
      responsible: "Rafay Usman",
      due: "30-Apr-25",
      remarks: "Overdue",
    },
    {
      number: "AC-INC-20230928-0068",
      category: "Incident",
      source: "Shift van hit a motorbike",
      description: "Complete ADMS System in all shift vans...",
      responsible: "Waseer Ahmad",
      due: "30-Apr-25",
      remarks: "Overdue",
    },
  ];

  const hazardsDeptSeries = [
    {
      name: "Hazards Logged",
      data: [1400, 1023, 865, 742, 560, 478, 321, 288, 244, 198, 165, 144, 120],
    },
  ];

  const hazardsDeptOptions = {
    chart: { type: "bar", background: "transparent", toolbar: { show: false } },
    xaxis: {
      categories: [
        "Production", "Engineering", "Warehouse", "Cold Storage", "Packing",
        "Utilities", "Maintenance", "QA", "Security", "Admin", "Waste Area",
        "Canteen", "Others"
      ],
      labels: {
        rotate: -45,
        style: { fontSize: "10px" },
      },
    },
    yaxis: {
      labels: {
        style: { fontSize: "10px" },
      },
    },
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: "40%",
      },
    },
    colors: ["#3b82f6"],
  };

  const hazardsStatusSeries = [
    {
      name: "Open",
      data: [120, 102, 95, 88, 76, 54, 47, 33, 22, 15],
    },
    {
      name: "Closed",
      data: [380, 320, 290, 255, 230, 198, 165, 140, 110, 88],
    },
  ];

  const hazardsStatusOptions = {
    chart: { type: "bar", stacked: true, background: "transparent", toolbar: { show: false } },
    xaxis: {
      categories: [
        "Admin", "Eng Head", "QA Head", "Safety Lead", "Ops Manager",
        "Maintenance", "Packing Head", "Warehouse", "Security", "Waste Lead"
      ],
      labels: {
        rotate: -45,
        style: { fontSize: "10px" },
      },
    },
    yaxis: {
      labels: { style: { fontSize: "10px" } },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "50%",
        borderRadius: 4,
      },
    },
    colors: ["#ef4444", "#10b981"],
    legend: {
      position: "top",
      labels: { colors: "#374151", fontSize: "12px" },
    },
  };

  // KPI Cards
  const incidentStats = {
    total: 14,
    open: 6,
    overdue: 2,
  };
  const incidentCategorySeries = [
    {
      name: "Incidents",
      data: [3, 4, 5, 2], // e.g.
    },
  ];
  const incidentCategoryOptions = {
    chart: { type: "bar", background: "transparent", toolbar: { show: false } },
    xaxis: {
      categories: [
        "Road Safety (Vehicular)",
        "Fire Risk",
        "Fall Risk",
        "Compressed Gas",
      ],
      labels: { rotate: -45, style: { fontSize: "10px" } },
    },
    yaxis: { labels: { style: { fontSize: "10px" } } },
    colors: ["#3b82f6"],
  };
  const incidentMonthlySeries = [
    { name: "Incidents", data: [3, 2, 4, 3, 2] },
  ];
  const incidentMonthlyOptions = {
    chart: { type: "line", toolbar: { show: false }, background: "transparent" },
    xaxis: {
      categories: ["January", "February", "March", "April", "May"],
      labels: { style: { fontSize: "10px" } },
    },
    yaxis: { labels: { style: { fontSize: "10px" } } },
    stroke: { curve: "smooth", width: 2 },
    colors: ["#f97316"],
  };
  const openIncidentsSeries = [
    {
      name: "Open Incidents",
      data: [1, 2, 2, 1],
    },
  ];
  const openIncidentsOptions = {
    chart: { type: "bar", toolbar: { show: false }, background: "transparent" },
    xaxis: {
      categories: ["adil sultan", "Daniyal Ahmed", "Faizan Arshad", "usman tahir"],
      labels: { rotate: -45, style: { fontSize: "10px" } },
    },
    yaxis: { labels: { style: { fontSize: "10px" } } },
    colors: ["#f43f5e"],
  };
  const safetyKPIData = [
    {
      name: "Abbas Raza",
      dept: "Ops Manager",
      scFort: 0, scTarget: 2, scMTD: 0,
      smsFort: 0, smsTarget: 2, smsMTD: 0,
      dcaMTD: 0, dcaTarget: 1,
      jccMTD: 1, jccTarget: 1,
    },
    {
      name: "Moazzam Ali",
      dept: "Production",
      scFort: 0, scTarget: 2, scMTD: 0,
      smsFort: 0, smsTarget: 2, smsMTD: 0,
      dcaMTD: 0, dcaTarget: 1,
      jccMTD: 1, jccTarget: 1,
    },
    {
      name: "Pervez Akhtar",
      dept: "Mixing / CS",
      scFort: 1, scTarget: 2, scMTD: 50,
      smsFort: 0, smsTarget: 2, smsMTD: 0,
      dcaMTD: 0, dcaTarget: 1,
      jccMTD: 0, jccTarget: 1,
    },
  ];


  const shiftSummaries = [
    {
      name: "Shift B Analysis (2PM - 10PM)",
      subtitle: "Evening shift performance metrics",
      stats: {
        alerts: 59,
        peak: "7PM",
        area: "Production",
        module: "Helmet",
      },
      bg: "#ecfdf5", // green tone
      color: "#065f46",
    },
    {
      name: "Shift C Analysis (10PM - 6AM)",
      subtitle: "Night shift performance metrics",
      stats: {
        alerts: 44,
        peak: "11PM",
        area: "Production",
        module: "Helmet",
      },
      bg: "#fefce8", // yellow tone
      color: "#92400e",
    },
  ];


  const headerStyle = (bgColor) => ({
    backgroundColor: bgColor,
    color: "white",
    padding: "12px 8px", // Slightly more vertical padding
    textAlign: "center",
    fontWeight: 600,
    borderRight: "1px solid white",
    fontSize: "13px", // Slightly smaller font
  });

  const cellStyle = (bgColor, isLeft = false) => ({
    backgroundColor: bgColor,
    color: "white",
    padding: "8px 12px", // More horizontal padding
    textAlign: isLeft ? "left" : "center",
    fontWeight: isLeft ? 500 : 400,
    fontSize: "12px", // Slightly smaller font
  });

  const data = [
    {
      name: "Fatality/Class A",
      data: [0, 0, 0],
    },
    {
      name: "LTA",
      data: [1, 1, 0],
    },
    {
      name: "RWC",
      data: [0, 0, 0],
    },
    {
      name: "MTC",
      data: [0, 0, 0],
    },
    {
      name: "FAC",
      data: [15, 3, 5],
    },
    {
      name: "Near Miss",
      data: [144, 57, 68],
    },
    {
      name: "Hazards",
      data: [10065, 4352, 4217],
    },
    {
      name: "SBO",
      data: [3346, 896, 1383],
    },
  ];

  const options = {
    chart: {
      type: "heatmap",
      toolbar: { show: false },
    },
    dataLabels: {
      enabled: true,
      style: {
        colors: ["#fff"],
      },
    },
    xaxis: {
      categories: ["2024 FY", "2024 YTD", "2025 YTD"],
      labels: {
        style: {
          fontWeight: 600,
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontWeight: 600,
        },
      },
    },
    plotOptions: {
      heatmap: {
        shadeIntensity: 0.5,
        radius: 4,
        useFillColorAsStroke: false,
        colorScale: {
          ranges: [
            { from: 0, to: 0, color: "#7f1d1d" },      // Fatality
            { from: 1, to: 1, color: "#be123c" },      // LTA
            { from: 2, to: 2, color: "#ef4444" },
            { from: 3, to: 10, color: "#f97316" },
            { from: 11, to: 100, color: "#fb923c" },
            { from: 101, to: 1000, color: "#facc15" },
            { from: 1001, to: 10000, color: "#eab308" },
            { from: 10001, to: 999999, color: "#fcd34d" },
          ],
        },
      },
    },
    title: {
      text: "Heinrich's Safety Pyramid Heatmap",
      align: "center",
      style: {
        fontSize: "18px",
        fontWeight: "bold",
      },
    },
  };



  const datee = new Date().toISOString()






  return (
    <div
      style={{
        minHeight: "100vh",
        backgroundColor: "#f8f9fa",
        padding: "14px",
        maxWidth: '1000px'
      }}
      id="report-page"
    >
      <div
        style={{

          margin: "0 auto",
          backgroundColor: "white",
          borderRadius: "8px",
          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
        }}
      >
        {/* Header */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            padding: "24px",
            borderBottom: "1px solid #e5e7eb",
            textAlign: "center",
          }}
        >
          <h1
            style={{
              fontSize: "2.25rem",
              fontWeight: "bold",
              color: "#111827",
              margin: "0 0 8px 0",
            }}
          >
            {filters.weekly !== '' ? 'Weekly' : filters.date !== '' ? 'Daily' : filters.month !== '' && 'Monthly'} Safety Report
          </h1>
          <p style={{ color: "#6b7280", margin: "0 0 12px 0" }}>
            Comprehensive {filters.date !== '' ? '24-Hour ' : filters.week !== '' ? 'Day to Day' : 'Weeks'}Analysis of Safety Performance
          </p>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "16px",
              fontSize: "14px",
              color: "#6b7280",
            }}
          >
            {/* <span>📅 Report Date: June 16, 2025</span> */}
            <span>• Coverage: {filters.week !== '' ? 'Whole Week' : filters.date !== '' ? '24 Hour' : 'Whole Month'}</span>
            <span>• Generated on {datee}</span>
          </div>
        </div>


        <div style={{ padding: "14px" }}>
          {/* Executive Summary */}
          <div style={{ marginBottom: "32px" }}>
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                borderLeft: "4px solid #3b82f6",
                paddingLeft: "12px",
                marginBottom: "20px",
              }}
            >
              Executive Summary
            </h2>

            <div
              style={{
                display: "flex",
                flexWrap: "wrap",
                justifyContent: 'space-evenly'


              }}
            >
              {/* Card 1 */}
              <div
                style={{
                  backgroundColor: "#fef2f2",
                  border: "1px solid #fecaca",
                  borderRadius: "8px",
                  padding: "16px",
                  textAlign: "center",
                  width: "220px",
                }}
              >
                <IoWarningOutline size={24} color="#dc2626" />
                <h3
                  style={{
                    fontSize: "2rem",
                    fontWeight: "bold",
                    color: "#dc2626",
                    margin: "8px 0 0",
                  }}
                >
                  {apiData.executiveSummary.totalAlerts}
                </h3>
                <p style={{ color: "#b91c1c", fontSize: "14px", margin: "0" }}>
                  Total Alerts
                </p>
              </div>

              {/* Card 2 */}
              <div
                style={{
                  backgroundColor: "#f0fdf4",
                  border: "1px solid #bbf7d0",
                  borderRadius: "8px",
                  padding: "16px",
                  textAlign: "center",
                  width: "220px",
                }}
              >
                <FaArrowTrendUp size={24} color="#16a34a" />
                <h3
                  style={{
                    fontSize: "2rem",
                    fontWeight: "bold",
                    color: "#16a34a",
                    margin: "8px 0 0",
                  }}
                >
                  {apiData?.executiveSummary?.avgCompliance}
                </h3>
                <p style={{ color: "#15803d", fontSize: "14px", margin: "0" }}>
                  Avg Compliance
                </p>
              </div>

              {/* Card 3 */}
              <div
                style={{
                  backgroundColor: "#eff6ff",
                  border: "1px solid #bfdbfe",
                  borderRadius: "8px",
                  padding: "16px",
                  textAlign: "center",
                  width: "220px",
                }}
              >
                <MdOutlineWatchLater size={24} color="#2563eb" />
                <h3
                  style={{
                    fontSize: "2rem",
                    fontWeight: "bold",
                    color: "#2563eb",
                    margin: "8px 0 0",
                  }}
                >
                  {apiData?.executiveSummary?.peakAlerts}
                </h3>
                <p style={{ color: "#1d4ed8", fontSize: "14px", margin: "0" }}>
                  Peak Alert Time
                </p>
              </div>

              {/* Card 4 */}
              <div
                style={{
                  backgroundColor: "#faf5ff",
                  border: "1px solid #d8b4fe",
                  borderRadius: "8px",
                  padding: "16px",
                  textAlign: "center",
                  width: "220px",
                }}
              >
                <BiMap size={24} color="#8b5cf6" />
                <h3
                  style={{
                    fontSize: "2rem",
                    fontWeight: "bold",
                    color: "#8b5cf6",
                    margin: "8px 0 0",
                  }}
                >
                  {apiData?.executiveSummary?.highRiskArea}
                </h3>
                <p style={{ color: "#8b5cf6", fontSize: "14px", margin: "0" }}>
                  High Risk Area
                </p>
              </div>
            </div>
          </div>


          {/* Heinrich's Safety Pyramid */}
          <div style={{ marginBottom: "32px" }}>
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                borderLeft: "4px solid #dc2626",
                paddingLeft: "12px",
                margin: "0 0 16px 0",
              }}
            >
              Heinrich's Safety Pyramid
            </h2>

            <div
              style={{
                backgroundColor: "white",
                border: "1px solid #e5e7eb",
                borderRadius: "8px",
                padding: "24px",
              }}
            >
              <div style={{ marginBottom: "16px" }}>
                <h3 style={{ fontSize: "1.125rem", fontWeight: "600", margin: "0 0 4px 0" }}>
                  Safety Performance Hierarchy
                </h3>
                <p style={{ color: "#6b7280", fontSize: "14px", margin: "0" }}>
                  Incident classification following Heinrich's pyramid model
                </p>
              </div>

              <div style={{ display: "flex", gap: "24px", alignItems: "flex-start" }}>
                {/* Pyramid Visual */}
                <div style={{ flexShrink: 0 }}>
                  <div
                    style={{

                      borderRadius: "8px",

                      textAlign: "center",
                    }}
                  >
                    <h5 style={{
                      fontWeight: "600", marginBottom: "12px", color: "#7f1d1d", backgroundColor: "#fef2f2",
                      padding: "10px", border: "2px solid #dc2626", borderRadius: "4px", marginBottom: '20px'
                    }}>
                      H-Pyramid
                    </h5>
                    <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: "10px" }}>
                      {pyramidData.map((item, index) => (
                        <div
                          key={index}
                          style={{
                            backgroundColor: item.color,
                            color: "#fff",
                            fontSize: "12px",
                            padding: "10px 8px",

                            width: `${100 + index * 10}px`,
                            textAlign: "center",
                          }}
                        >
                          {item.level}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Grid Table */}
                <div style={{ flex: 1 }}>
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(4, 1fr)",
                      gap: "8px",
                    }}
                  >
                    {/* Headers */}
                    {["Category", "2024 FY", "2024 YTD", "2025 YTD"].map((header, i) => (
                      <div
                        key={`head-${i}`}
                        style={{
                          backgroundColor: "#dc2626",
                          color: "#fff",
                          padding: "12px",
                          fontWeight: "600",
                          textAlign: "center",

                        }}
                      >
                        {header}
                      </div>
                    ))}

                    {/* Data Rows */}
                    {pyramidData.map((item, rowIndex) => (
                      <>
                        <div
                          key={`cat-${rowIndex}`}
                          style={{
                            backgroundColor: item.color,
                            color: "#fff",
                            padding: "12px",
                            textAlign: "center",

                            fontWeight: "500",
                          }}
                        >
                          {item.level}
                        </div>
                        {[item.fy2024, item.ytd2024, item.ytd2025].map((val, i) => (
                          <div
                            key={`val-${rowIndex}-${i}`}
                            style={{
                              backgroundColor: item.color,
                              color: "#fff",
                              padding: "12px",
                              textAlign: "center",

                              fontWeight: "500",
                            }}
                          >
                            {val.toLocaleString()}
                          </div>
                        ))}
                      </>
                    ))}
                  </div>
                </div>
              </div>

            </div>
          </div>


          {/* Time-Based Alert Analysis */}
          <div>
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                marginBottom: "16px",
                borderLeft: "4px solid #3b82f6",
                paddingLeft: "12px",
                margin: "0 0 16px 0",
              }}
            >
              ⏰ Time-Based Alert Analysis
            </h2>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(500px, 1fr))",
                gap: "24px",
              }}
            >
              <div
                style={{
                  backgroundColor: "white",
                  border: "1px solid #e5e7eb",
                  borderRadius: "8px",
                  padding: "24px",
                }}
              >
                <div style={{ marginBottom: "12px" }}>
                  <h3
                    style={{
                      fontSize: "1.125rem",
                      fontWeight: "600",
                      margin: "0 0 4px 0",
                    }}
                  >
                    24-Hour Alert Distribution
                  </h3>
                  <p
                    style={{
                      color: "#6b7280",
                      fontSize: "14px",
                      margin: "0",
                    }}
                  >
                    Hourly breakdown of alerts across the entire day
                  </p>
                </div>

                <Chart
                  //  key={isPdfRendering ? "pdf-mode-chart" : "live-mode-chart"}

                  options={lineChartOptions}
                  series={lineChartSeries}
                  type="line"
                  height={200}
                />



              </div>

              <div
                style={{
                  backgroundColor: "white",
                  border: "1px solid #e5e7eb",
                  borderRadius: "8px",
                  padding: "24px",
                }}
              >
                <div style={{ marginBottom: "12px" }}>
                  <h3
                    style={{
                      fontSize: "1.125rem",
                      fontWeight: "600",
                      margin: "0 0 4px 0",
                    }}
                  >
                    Shift-Based Comparison
                  </h3>
                  <p
                    style={{
                      color: "#6b7280",
                      fontSize: "14px",
                      margin: "0",
                    }}
                  >
                    Alert patterns across three 8-hour shifts
                  </p>
                </div>

                <Chart
                  //  key={'xyz'}
                  options={shiftChartOptions}
                  series={shiftChartSeries}
                  type="line"
                  height={300}
                />


              </div>
            </div>
          </div>
          {/* Area-Based Alert Analysis */}
          <div style={{ marginTop: "32px" }}>
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                marginBottom: "16px",
                borderLeft: "4px solid #10b981",
                paddingLeft: "12px",
                margin: "0 0 16px 0",
              }}
            >
              📍 Area-Based Alert Analysis
            </h2>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(400px, 1fr))",
                gap: "24px",
              }}
            >
              {/* Chart 1: Total Alerts by Area */}
              <div
                style={{
                  backgroundColor: "white",
                  border: "1px solid #e5e7eb",
                  borderRadius: "8px",
                  padding: "24px",
                }}
              >
                <h3 style={{ fontSize: "1.125rem", fontWeight: "600", marginBottom: "8px" }}>
                  Total Alerts by Area
                </h3>
                <Chart options={areaBarOptions} series={areaBarSeries} type="bar" height={300} />
              </div>

              {/* Chart 2: Alerts by Area and Shift */}
              <div
                style={{
                  backgroundColor: "white",
                  border: "1px solid #e5e7eb",
                  borderRadius: "8px",
                  padding: "24px",
                }}
              >
                <h3 style={{ fontSize: "1.125rem", fontWeight: "600", marginBottom: "8px" }}>
                  Alerts by Area and Shift
                </h3>
                <Chart options={areaShiftOptions} series={areaShiftSeries} type="bar" height={300} />
              </div>
            </div>

            {/* Key Findings */}
            <div
              style={{
                marginTop: "16px",
                backgroundColor: "#f9fafb",
                border: "1px solid #e5e7eb",
                borderRadius: "8px",
                padding: "16px",
              }}
            >
              <h4
                style={{
                  fontSize: "1rem",
                  fontWeight: "600",
                  marginBottom: "8px",
                  color: "#111827",
                }}
              >
                Key Findings:
              </h4>
              <ul style={{ margin: "0", paddingLeft: "20px", color: "#374151", fontSize: "14px" }}>
                <li>Production Floor shows highest activity across all shifts (45 total alerts)</li>
                <li>Shift A consistently shows higher alert rates in most areas</li>
                <li>Loading Dock shows peak activity during Shift B (12 alerts)</li>
                <li>Office Area maintains consistently low alert rates across all shifts</li>
              </ul>
            </div>
          </div>
          {/* Module-Based Alert Analysis */}
          <div style={{ marginTop: "32px" }}>
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                marginBottom: "16px",
                borderLeft: "4px solid #f97316",
                paddingLeft: "12px",
                margin: "0 0 16px 0",
              }}
            >
              ⚙️ Module-Based Alert Analysis
            </h2>

            <div
              style={{
                backgroundColor: "white",
                border: "1px solid #e5e7eb",
                borderRadius: "8px",
                padding: "24px",
                marginBottom: "24px",
              }}
            >
              <h3 style={{ fontSize: "1.125rem", fontWeight: "600", marginBottom: "8px" }}>
                Overall Module Distribution
              </h3>
              <p style={{ fontSize: "14px", color: "#6b7280", marginBottom: "16px" }}>
                Total alerts by safety module type
              </p>
              <Chart options={modulePieOptions} series={modulePieData.series} type="pie" height={300} />
            </div>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(220px, 1fr))",
                gap: "16px",
              }}
            >
              {[
                { label: "Helmet Alerts by Shift", icon: "🪖" },
                { label: "Vest Alerts by Shift", icon: "🎽" },
                { label: "Forklift Alerts by Shift", icon: "🚜" },
                { label: "Emergency Exit Alerts by Shift", icon: "🚪" },
              ].map((item, index) => (
                <div
                  key={index}
                  style={{
                    backgroundColor: "white",
                    border: "1px solid #e5e7eb",
                    borderRadius: "8px",
                    padding: "16px",
                    textAlign: "center",
                  }}
                >
                  <p style={{ fontSize: "14px", fontWeight: "600", marginBottom: "8px" }}>
                    {item.icon} {item.label}
                  </p>
                  <Chart options={shiftPieOptions} series={shiftPieSeriess} type="pie" height={200} />
                </div>
              ))}
            </div>
          </div>
          {/* Compliance Analysis by Sub-Area */}
          <div style={{ marginTop: "32px" }}>
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                marginBottom: "16px",
                borderLeft: "4px solid #8b5cf6",
                paddingLeft: "12px",
                margin: "0 0 16px 0",
              }}
            >
              🧭 Compliance Analysis by Sub-Area
            </h2>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(400px, 1fr))",
                gap: "24px",
              }}
            >
              {/* Compliance Metrics */}
              <div>
                <h3 style={{ fontSize: "1.125rem", fontWeight: "600", marginBottom: "12px" }}>
                  Sub-Area Compliance Metrics
                </h3>
                <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
                  {complianceMetrics.map((zone, idx) => (
                    <div
                      key={idx}
                      style={{
                        backgroundColor: zone.color,
                        padding: "12px 16px",
                        borderRadius: "8px",
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        fontSize: "14px",
                        border: "1px solid #e5e7eb",
                      }}
                    >
                      <div>
                        <strong>{zone.zone}</strong>
                        <div>Compliance: {zone.compliance}%</div>
                      </div>
                      <div style={{ textAlign: "right" }}>
                        <div style={{ fontWeight: "600" }}>{zone.label}</div>
                        <div style={{ color: "#4b5563" }}>Alerts: {zone.alerts}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Chart */}
              <div>
                <h3 style={{ fontSize: "1.125rem", fontWeight: "600", marginBottom: "12px" }}>
                  Sub-Area Alerts by Shift
                </h3>
                <Chart options={subAreaShiftOptions} series={subAreaShiftSeries} type="bar" height={300} />
              </div>
            </div>
          </div>

          {/* Individual Shift Analysis */}
          {/* <div style={{ marginTop: "32px" }}>
  <h2
    style={{
      fontSize: "1.25rem",
      fontWeight: "600",
      marginBottom: "16px",
      borderLeft: "4px solid #3b82f6",
      paddingLeft: "12px",
      margin: "0 0 16px 0",
    }}
  >
    👤 Individual Shift Analysis
  </h2>

  <div
    style={{
      backgroundColor: "#f0f9ff",
      border: "1px solid #c7d2fe",
      borderRadius: "8px",
      padding: "24px",
    }}
  >
    <p style={{ fontWeight: "600", fontSize: "16px", color: "#1e3a8a", marginBottom: "4px" }}>
      Shift A Analysis (6AM – 2PM)
    </p>
    <p style={{ fontSize: "14px", color: "#6b7280", marginBottom: "16px" }}>
      Day shift performance metrics
    </p>

    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(100px, 1fr))",
        gap: "16px",
        textAlign: "center",
      }}
    >
      <div>
        <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#1e40af", margin: 0 }}>
          {shiftAStats.totalAlerts}
        </h3>
        <p style={{ fontSize: "14px", color: "#1e3a8a", margin: 0 }}>Total Alerts</p>
      </div>
      <div>
        <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#1e40af", margin: 0 }}>
          {shiftAStats.peakHour}
        </h3>
        <p style={{ fontSize: "14px", color: "#1e3a8a", margin: 0 }}>Peak Hour</p>
      </div>
      <div>
        <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#1e40af", margin: 0 }}>
          {shiftAStats.topArea}
        </h3>
        <p style={{ fontSize: "14px", color: "#1e3a8a", margin: 0 }}>Top Area</p>
      </div>
      <div>
        <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#1e40af", margin: 0 }}>
          {shiftAStats.topModule}
        </h3>
        <p style={{ fontSize: "14px", color: "#1e3a8a", margin: 0 }}>Top Module</p>
      </div>
    </div>
  </div>
</div> */}
          {/* Near-Miss Analysis YTD 2025 */}
          <div style={{ marginTop: "32px" }}>
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                marginBottom: "16px",
                borderLeft: "4px solid #3b82f6",
                paddingLeft: "12px",
                color: "#1d4ed8",
              }}
            >
              📘 Near-Miss Analysis YTD 2025
            </h2>

            {/* KPI Summary */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(160px, 1fr))",
                gap: "16px",
                marginBottom: "24px",
              }}
            >
              <div
                style={{
                  backgroundColor: "#fee2e2",
                  borderRadius: "8px",
                  padding: "16px",
                  textAlign: "center",
                }}
              >
                <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#b91c1c", margin: 0 }}>3</h3>
                <p style={{ fontSize: "14px", color: "#b91c1c", margin: 0 }}>Overdue</p>
              </div>
              <div
                style={{
                  backgroundColor: "#dbeafe",
                  borderRadius: "8px",
                  padding: "16px",
                  textAlign: "center",
                }}
              >
                <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#1d4ed8", margin: 0 }}>77</h3>
                <p style={{ fontSize: "14px", color: "#1d4ed8", margin: 0 }}>Total Near Misses</p>
              </div>
              <div
                style={{
                  backgroundColor: "#fef3c7",
                  borderRadius: "8px",
                  padding: "16px",
                  textAlign: "center",
                }}
              >
                <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#b45309", margin: 0 }}>25</h3>
                <p style={{ fontSize: "14px", color: "#b45309", margin: 0 }}>Open Near-misses</p>
              </div>
            </div>

            {/* Charts */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(350px, 1fr))",
                gap: "24px",
              }}
            >
              {/* Area Chart */}
              <div
                style={{
                  backgroundColor: "#fff",
                  border: "2px solid #facc15",
                  borderRadius: "8px",
                  padding: "16px",
                  overflow: "hidden",
                }}
              >
                <h3 style={{ fontSize: "1rem", fontWeight: "600", color: "#1d4ed8", marginBottom: "8px" }}>
                  Area Wise Near Misses Spread YTD 2025
                </h3>
                <div style={{ width: "100%", overflowX: "auto" }}>
                  <Chart options={areaChartOptions} series={areaChartSeries} type="bar" height={300} />
                </div>
              </div>

              {/* Monthly Chart */}
              <div
                style={{
                  backgroundColor: "#fff",
                  border: "2px solid #facc15",
                  borderRadius: "8px",
                  padding: "16px",
                  overflow: "hidden",
                }}
              >
                <h3 style={{ fontSize: "1rem", fontWeight: "600", color: "#1d4ed8", marginBottom: "8px" }}>
                  Near Misses Insights YTD 2025
                </h3>
                <Chart options={monthlyChartOptions} series={monthlyChartSeries} type="line" height={300} />
              </div>
            </div>
          </div>

          {/* Open Near Misses Responsibility Wise */}
          <div style={{ marginTop: "32px" }}>
            <h3
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#1d4ed8",
                marginBottom: "8px",
              }}
            >
              📊 Open Near Misses Responsibility Wise YTD 2025 (25)
            </h3>
            <div
              style={{
                backgroundColor: "white",
                border: "2px solid #facc15",
                borderRadius: "8px",
                padding: "16px",
                overflowX: "auto",
              }}
            >
              <Chart options={openNearMissesOptions} series={openNearMissesSeries} type="bar" height={300} />
            </div>
          </div>

          {/* Incident Action Items Analysis */}
          <div style={{ marginTop: "32px" }}>
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                marginBottom: "16px",
                borderLeft: "4px solid #3b82f6",
                paddingLeft: "12px",
                color: "#1d4ed8",
              }}
            >
              🧾 Incidents Action Items Analysis YTD 2025
            </h2>

            {/* KPI Cards */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(160px, 1fr))",
                gap: "16px",
                marginBottom: "24px",
              }}
            >
              <div style={{ backgroundColor: "#dcfce7", padding: "16px", textAlign: "center", borderRadius: "8px" }}>
                <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#15803d", margin: 0 }}>69%</h3>
                <p style={{ fontSize: "14px", color: "#15803d", margin: 0 }}>Closure Rate</p>
              </div>
              <div style={{ backgroundColor: "#dbeafe", padding: "16px", textAlign: "center", borderRadius: "8px" }}>
                <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#1d4ed8", margin: 0 }}>13</h3>
                <p style={{ fontSize: "14px", color: "#1d4ed8", margin: 0 }}>Open Action Items</p>
              </div>
              <div style={{ backgroundColor: "#fee2e2", padding: "16px", textAlign: "center", borderRadius: "8px" }}>
                <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#b91c1c", margin: 0 }}>2</h3>
                <p style={{ fontSize: "14px", color: "#b91c1c", margin: 0 }}>Overdue Action Items</p>
              </div>
            </div>

            {/* Incident Actions Chart */}
            <div
              style={{
                backgroundColor: "white",
                border: "2px solid #86efac",
                borderRadius: "8px",
                padding: "16px",
                overflowX: "auto",
              }}
            >
              <h3 style={{ fontSize: "1rem", fontWeight: "600", color: "#16a34a", marginBottom: "8px" }}>
                Incident Actions (13 Open Actions)
              </h3>
              <Chart options={incidentActionOptions} series={incidentActionSeries} type="bar" height={300} />
            </div>
          </div>

          {/* Action Items Details Table */}
          <div style={{ marginTop: "32px" }}>
            <h3 style={{ fontSize: "1.125rem", fontWeight: "600", marginBottom: "12px" }}>
              📝 Action Items Details
            </h3>
            <div style={{ overflowX: "auto" }}>
              <table style={{ width: "100%", borderCollapse: "collapse", fontSize: "14px" }}>
                <thead style={{ backgroundColor: "#f3f4f6" }}>
                  <tr>
                    <th style={{ padding: "8px", border: "1px solid #e5e7eb" }}>Action Number</th>
                    <th style={{ padding: "8px", border: "1px solid #e5e7eb" }}>Source Category</th>
                    <th style={{ padding: "8px", border: "1px solid #e5e7eb" }}>Source</th>
                    <th style={{ padding: "8px", border: "1px solid #e5e7eb" }}>Description</th>
                    <th style={{ padding: "8px", border: "1px solid #e5e7eb" }}>Person Responsible</th>
                    <th style={{ padding: "8px", border: "1px solid #e5e7eb" }}>Due Date</th>
                    <th style={{ padding: "8px", border: "1px solid #e5e7eb" }}>Remarks</th>
                  </tr>
                </thead>
                <tbody>
                  {actionItems.map((item, i) => (
                    <tr key={i}>
                      <td style={{ padding: "8px", border: "1px solid #e5e7eb" }}>{item.number}</td>
                      <td style={{ padding: "8px", border: "1px solid #e5e7eb" }}>{item.category}</td>
                      <td style={{ padding: "8px", border: "1px solid #e5e7eb" }}>{item.source}</td>
                      <td style={{ padding: "8px", border: "1px solid #e5e7eb" }}>{item.description}</td>
                      <td style={{ padding: "8px", border: "1px solid #e5e7eb" }}>{item.responsible}</td>
                      <td style={{ padding: "8px", border: "1px solid #e5e7eb" }}>{item.due}</td>
                      <td style={{ padding: "8px", border: "1px solid #e5e7eb", color: "#b91c1c", fontWeight: "600" }}>
                        {item.remarks}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Hazards Analysis YTD 2025 */}
          <div style={{ marginTop: "32px" }}>
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                marginBottom: "16px",
                borderLeft: "4px solid #3b82f6",
                paddingLeft: "12px",
                color: "#1d4ed8",
              }}
            >
              🧯 Hazards Analysis YTD 2025
            </h2>

            {/* Chart 1: Department-Wise Hazards */}
            <div
              style={{
                backgroundColor: "white",
                border: "2px solid #facc15",
                borderRadius: "8px",
                padding: "16px",
                marginBottom: "24px",
                overflowX: "auto",
              }}
            >
              <h3 style={{ fontSize: "1rem", fontWeight: "600", color: "#1d4ed8", marginBottom: "8px" }}>
                Department Wise Hazards Log Spread (Total)
              </h3>
              <Chart options={hazardsDeptOptions} series={hazardsDeptSeries} type="bar" height={300} />
            </div>

            {/* Chart 2: Responsibility Wise Open vs Closed */}
            <div
              style={{
                backgroundColor: "white",
                border: "2px solid #facc15",
                borderRadius: "8px",
                padding: "16px",
                overflowX: "auto",
              }}
            >
              <h3 style={{ fontSize: "1rem", fontWeight: "600", color: "#1d4ed8", marginBottom: "8px" }}>
                Open vs Closed Hazards Responsibility wise (2025 Log)
              </h3>
              <Chart options={hazardsStatusOptions} series={hazardsStatusSeries} type="bar" height={300} />
            </div>
          </div>

          {/* Incidents Analysis YTD 2025 */}
          <div style={{ marginTop: "32px" }}>
            <h2
              style={{
                fontSize: "1.25rem",
                fontWeight: "600",
                marginBottom: "16px",
                borderLeft: "4px solid #3b82f6",
                paddingLeft: "12px",
                color: "#1d4ed8",
              }}
            >
              🚨 Incidents Analysis YTD 2025
            </h2>

            {/* KPI Cards */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(160px, 1fr))",
                gap: "16px",
                marginBottom: "24px",
              }}
            >
              <div style={{ backgroundColor: "#fee2e2", padding: "16px", borderRadius: "8px", textAlign: "center" }}>
                <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#b91c1c", margin: 0 }}>
                  {incidentStats.overdue}
                </h3>
                <p style={{ fontSize: "14px", color: "#b91c1c", margin: 0 }}>Overdue</p>
              </div>
              <div style={{ backgroundColor: "#dbeafe", padding: "16px", borderRadius: "8px", textAlign: "center" }}>
                <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#1d4ed8", margin: 0 }}>
                  {incidentStats.total}
                </h3>
                <p style={{ fontSize: "14px", color: "#1d4ed8", margin: 0 }}>Total Incidents</p>
              </div>
              <div style={{ backgroundColor: "#fef3c7", padding: "16px", borderRadius: "8px", textAlign: "center" }}>
                <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: "#b45309", margin: 0 }}>
                  0{incidentStats.open}
                </h3>
                <p style={{ fontSize: "14px", color: "#b45309", margin: 0 }}>Open Incidents</p>
              </div>
            </div>

            {/* Category & Month-wise Charts */}
            <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))", gap: "24px" }}>
              <div
                style={{
                  backgroundColor: "white",
                  border: "2px solid #facc15",
                  borderRadius: "8px",
                  padding: "16px",
                  overflowX: "auto",
                }}
              >
                <h3 style={{ fontSize: "1rem", fontWeight: "600", color: "#1d4ed8" }}>
                  Category Wise Incidents Spread YTD 2025
                </h3>
                <Chart options={incidentCategoryOptions} series={incidentCategorySeries} type="bar" height={300} />
              </div>
              <div
                style={{
                  backgroundColor: "white",
                  border: "2px solid #facc15",
                  borderRadius: "8px",
                  padding: "16px",
                }}
              >
                <h3 style={{ fontSize: "1rem", fontWeight: "600", color: "#1d4ed8" }}>
                  Incidents Insights YTD 2025
                </h3>
                <Chart options={incidentMonthlyOptions} series={incidentMonthlySeries} type="line" height={300} />
              </div>
            </div>

            {/* Responsibility-Wise Open Incidents */}
            <div
              style={{
                marginTop: "24px",
                backgroundColor: "white",
                border: "2px solid #facc15",
                borderRadius: "8px",
                padding: "16px",
                overflowX: "auto",
              }}
            >
              <h3 style={{ fontSize: "1rem", fontWeight: "600", color: "#1d4ed8" }}>
                Open Incidents Responsibility Wise YTD 2025 <span style={{ color: "#b91c1c" }}> (2 Overdue)</span>
              </h3>
              <Chart options={openIncidentsOptions} series={openIncidentsSeries} type="bar" height={300} />
            </div>
          </div>

          {/* Safety KPIs Table */}
          <div style={{ marginTop: "32px" }}>
            <h3
              style={{
                fontSize: "1.125rem",
                fontWeight: "600",
                marginBottom: "12px",
                color: "#1e3a8a",
              }}
            >
              📊 Safety KPI's Monitoring ICF - 17-06-2025
            </h3>
            <div style={{ overflowX: "auto" }}>
              <table style={{ width: "100%", borderCollapse: "collapse", fontSize: "13px" }}>
                <thead style={{ backgroundColor: "#e0f2fe", color: "#1e40af" }}>
                  <tr>
                    <th style={{ padding: "8px", border: "1px solid #e5e7eb" }}>S#</th>
                    <th style={{ padding: "8px", border: "1px solid #e5e7eb" }}>Employee Name</th>
                    <th style={{ padding: "8px", border: "1px solid #e5e7eb" }}>Department</th>
                    <th colSpan={3} style={{ padding: "8px", border: "1px solid #e5e7eb" }}>Safety Contacts</th>
                    <th colSpan={3} style={{ padding: "8px", border: "1px solid #e5e7eb" }}>SMS</th>
                    <th colSpan={2} style={{ padding: "8px", border: "1px solid #e5e7eb" }}>DCA</th>
                    <th colSpan={2} style={{ padding: "8px", border: "1px solid #e5e7eb" }}>JCC</th>
                  </tr>
                </thead>
                <tbody>
                  {safetyKPIData.map((row, index) => (
                    <tr key={index}>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{index + 1}</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.name}</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.dept}</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.scFort}</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.scTarget}</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.scMTD}%</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.smsFort}</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.smsTarget}</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.smsMTD}%</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.dcaMTD}</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.dcaTarget}</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.jccMTD}</td>
                      <td style={{ padding: "6px", border: "1px solid #e5e7eb" }}>{row.jccTarget}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Shift B & C Analysis */}
          <div style={{ marginTop: "32px" }}>
            {shiftSummaries.map((shift, i) => (
              <div
                key={i}
                style={{
                  backgroundColor: shift.bg,
                  border: "1px solid #e5e7eb",
                  borderRadius: "8px",
                  padding: "24px",
                  marginBottom: "16px",
                }}
              >
                <p style={{ fontWeight: "600", fontSize: "16px", color: shift.color, marginBottom: "4px" }}>
                  {shift.name}
                </p>
                <p style={{ fontSize: "14px", color: "#6b7280", marginBottom: "16px" }}>
                  {shift.subtitle}
                </p>

                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(auto-fit, minmax(100px, 1fr))",
                    gap: "16px",
                    textAlign: "center",
                  }}
                >
                  <div>
                    <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: shift.color, margin: 0 }}>
                      {shift.stats.alerts}
                    </h3>
                    <p style={{ fontSize: "14px", color: shift.color, margin: 0 }}>Total Alerts</p>
                  </div>
                  <div>
                    <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: shift.color, margin: 0 }}>
                      {shift.stats.peak}
                    </h3>
                    <p style={{ fontSize: "14px", color: shift.color, margin: 0 }}>Peak Hour</p>
                  </div>
                  <div>
                    <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: shift.color, margin: 0 }}>
                      {shift.stats.area}
                    </h3>
                    <p style={{ fontSize: "14px", color: shift.color, margin: 0 }}>Top Area</p>
                  </div>
                  <div>
                    <h3 style={{ fontSize: "1.5rem", fontWeight: "bold", color: shift.color, margin: 0 }}>
                      {shift.stats.module}
                    </h3>
                    <p style={{ fontSize: "14px", color: shift.color, margin: 0 }}>Top Module</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {/* Report Footer */}
          <p style={{ fontSize: "12px", color: "#6b7280", textAlign: "center", marginTop: "24px" }}>
            This comprehensive daily safety report was generated on June 16, 2025 at 6:45:01 PM.<br />
            For questions or concerns, contact the Safety Department at <a href="mailto:<EMAIL>"><EMAIL></a>
          </p>






        </div>
      </div>
    </div>
  )
};

export default DailySafetyReport;
