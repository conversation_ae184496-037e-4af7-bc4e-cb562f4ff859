import React, { useEffect, useRef, useState } from 'react';
import { io } from 'socket.io-client';  // Import Socket.IO client
import { errorToast } from '../../_helper/helper';

export default function MicConnectionWithSocketIO({
  socketUrl = 'wss://beunileverchatbot.disruptlabs.tech/stt/transcribe', // your backend URL
  socketPath = '/stt/transcribe', // adjust the path as per your server's endpoint
  sessionId,
  getOrCreateSessionId,
  onPartialTranscript,
  onFinalTranscript,
  disabled = false,
}) {
  const [isRecording, setIsRecording] = useState(false);
  const [audioMimeType, setAudioMimeType] = useState(''); // mime type of audio format
  const socketRef = useRef(null);
  const mediaStreamRef = useRef(null);
  const mediaRecorderRef = useRef(null);

  // Initialize the mime type for the MediaRecorder
  useEffect(() => {
    const candidates = ['audio/webm;codecs=opus', 'audio/ogg;codecs=opus'];
    const picked = candidates.find(m => window.MediaRecorder && MediaRecorder.isTypeSupported(m));
    setAudioMimeType(picked || '');
  }, []);

const connectSocket = async () => {
    const sid = sessionId || (await getOrCreateSessionId());
    
    // Disconnect the previous socket connection if it exists
    if (socketRef.current) {
      try {
        socketRef.current.disconnect();
      } catch (err) {
        console.error('Error disconnecting existing socket:', err);
      }
    }

    try {
      // Log the WebSocket URL
      const wsProtocol = socketUrl.startsWith('https') ? 'wss' : 'ws';  // Secure vs insecure WebSocket
      const ttsWsUrl = `${socketUrl.replace(/^https?/, wsProtocol)}`;
      console.log("Connecting to WebSocket at:", ttsWsUrl);  // Log WebSocket URL

      // Create and connect to the socket
      const socket = io(ttsWsUrl, {
        path: socketPath,
        transports: ['websocket'],
        auth: { session_id: sid },
        forceNew: true,
        timeout: 10000,
      });

      // WebSocket event listeners
      socket.on('connect', () => {
        console.log('WebSocket connected');
      });

      socket.on('connect_error', (err) => {
        console.error('WebSocket connect error:', err);
        stopRecordingAndClose();
      });

      socket.on('disconnect', () => {
        console.log('WebSocket disconnected');
        if (isRecording) stopRecordingAndClose();
      });

      socket.on('transcription:partial', (data) => {
        console.log('Received partial transcription:', data);
        if (data?.text) onPartialTranscript(data.text);
      });

      socket.on('transcription:final', (data) => {
        console.log('Received final transcription:', data);
        if (data?.text) onFinalTranscript(data.text);
      });

      socketRef.current = socket;
      return socket;
    } catch (err) {
      console.error('Socket connection error:', err);
      stopRecordingAndClose();
      throw err;  // Rethrow the error to handle it in the caller
    }
  };


  const getMicStream = async () => {
    try {
      return await navigator.mediaDevices.getUserMedia({ audio: true }); // Request microphone access
    } catch (err) {
      throw new Error('Microphone access error');
    }
  };

  const startMediaRecorder = (stream, socket) => {
    try {
      const mr = new MediaRecorder(stream, { mimeType: audioMimeType });
      mediaRecorderRef.current = mr;

      mr.ondataavailable = async (evt) => {
        if (!evt.data || evt.data.size === 0) return;
        const buf = await evt.data.arrayBuffer();
        if (socket.connected) socket.emit('audio:chunk', buf);  // Send audio chunk via socket
      };

      mr.onerror = () => stopRecordingAndClose();
      mr.start(250);  // Start recording in chunks of 250ms
    } catch (err) {
      console.error('Failed to start media recorder', err);
      stopRecordingAndClose();
    }
  };

  const handleMicClick = async () => {
    if (isRecording) {
      stopRecordingAndClose();  // Stop if already recording
      return;
    }
    try {
      const stream = await getMicStream();  // Get microphone stream
      const socket = await connectSocket();  // Connect socket
      socket.emit('audio:start', { mime_type: audioMimeType, session_id: sessionId }); // Send audio start signal
      startMediaRecorder(stream, socket);  // Start recording and sending audio
      setIsRecording(true);  // Set recording state to true
    } catch (err) {
      console.error('Mic error', err);
      setIsRecording(false);
    }
  };

  const stopRecordingAndClose = () => {
    setIsRecording(false);  // Set recording state to false
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      try { mediaRecorderRef.current.stop(); } catch {}  // Stop recording
    }
    mediaRecorderRef.current = null;
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(t => t.stop());  // Stop all media tracks
      mediaStreamRef.current = null;
    }
    if (socketRef.current) {
      try { socketRef.current.disconnect(); } catch {}  // Disconnect the socket
      socketRef.current = null;
    }
  };

  useEffect(() => {
    return () => stopRecordingAndClose();  // Cleanup on component unmount
  }, []);

  return (
    <div>
      {/* Mic selector */}
      <button onClick={handleMicClick} disabled={disabled}>
        {isRecording ? 'Stop' : '🎤 Start Recording'}
      </button>
    </div>
  );
}
