.chatbox-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f7f8fc;
  font-family: 'Segoe UI', sans-serif;
}

.chat-messages-section {
  flex: 1;
  overflow-y: auto;
  padding: 10px 16px;
  background: #f7f8fc;
  overflow-x: hidden;
  word-break: break-word;
  padding-right: 10px;
}

.message-bubble {
  max-width: 90%;
  margin-bottom: 12px;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
  word-break: break-word;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.user {
  align-self: flex-end;
  margin-left: auto;
  background: #3470a1;
  color: white;
}

.ai {
  background-color: #f2f2f2;
  align-self: flex-start;
  margin-right: auto;
  color: #222;
}

.timestamp {
  font-size: 11px;
  color: #888;
  margin-top: 6px;
  text-align: right;
}

.initial-prompt-box {
  display: flex;
  justify-content: center;
  padding: 10px;
  border-top: 1px solid #ddd;
  background-color: #ffffff;
  cursor: text;
}

.initial-input {
  width: 100%;
  max-width: 95%;
  padding: 10px 16px;
  border: 1px solid #ccc;
  border-radius: 20px;
  font-size: 14px;
  color: #888;
  pointer-events: none;
  background-color: #f9f9f9;
}

.transition-footer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  opacity: 0;
}

.transition-footer.expanded {
  max-height: 300px;
  opacity: 1;
  background-color: #fff;
  padding: 10px 16px;
}

.chat-input-form textarea {
  width: 100%;
  border: none;
  border-radius: 10px;
  padding: 12px;
  resize: vertical;
  font-size: 14px;
  background-color: #f9f9f9;
  box-shadow: none;
  outline: none;
}

.chat-input-buttons {
  display: flex;
  gap: 6px;
  justify-content: flex-end;
  margin-top: 10px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.btn.border {
  background: #f1f1f1;
  border: 1px solid #ccc;
}

/* Center footer when there are no messages */
.chatbox-container.center-footer {
  justify-content: center;
  align-items: center;
  padding: 0 16px;
}

.chatbox-container.center-footer .chat-messages-section {
  display: none;
}

.chatbox-container.center-footer .transition-footer.expanded {
  max-height: none;
  opacity: 1;
  background-color: #fff;
  padding: 16px;
  width: 100%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
}

.blinking-dots::after {
  content: '';
  display: inline-block;
  width: 1em;
  text-align: left;
  animation: dots 1.5s steps(3, end) infinite;
}

@keyframes dots {
  0% { content: ''; }
  33% { content: '.'; }
  66% { content: '..'; }
  100% { content: '...'; }
}

 

.chat-messages-section table {
  width: 100%;
  overflow-x: auto;
  display: block;
}

.chat-messages-section th,
.chat-messages-section td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.chat-messages-section th {
  background-color: #f2f2f2;
  font-weight: 600;
}
.chat-messages-section td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
  word-break: break-word; /* ✅ wrap long URLs */
  max-width: 100%;
}

.chat-messages-section tbody tr:nth-child(even) {
  background-color: #fafafa;
}


