import { useEffect, useRef, useState, useCallback } from 'react';
import DailySafetyReport from './NewPDF';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import AreaService from '../../../../../api/areaService';
import { executive_summary_for_report, get_alerts_for_report } from '../../../../../RenderPDFformat/utils/reportApiUtils';
import { toast } from 'react-toastify';
import Loader1 from '../../../../../CommonElements/Spinner/loader';
import * as XLSX from 'xlsx'; // Import xlsx

const PdfRenderer = ({ onDone, filters }) => {
  const hasRunRef = useRef(false);
  const timerRef = useRef(null);
  const componentRef = useRef(null);
  const [generatingPDF, setGeneratingPDF] = useState(false);
  const [loader, setLoader] = useState(false);

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [file, setFile] = useState(null);

  // State for storing parsed Excel data
  const [excelData, setExcelData] = useState({});

  // Callback to handle modal file input
  const handleFileChange = (event) => {
    const uploadedFile = event.target.files[0];
    setFile(uploadedFile);

    // Parse the Excel file
    if (uploadedFile) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const data = e.target.result;
        const workbook = XLSX.read(data, { type: 'binary' });

        // Assuming the first sheet is the one we want
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

        // Convert the sheet to JSON
        const sheetData = XLSX.utils.sheet_to_json(firstSheet);

        // Map the sheetData to an object with column names as keys
        const formattedData = sheetData.reduce((acc, row) => {
          Object.keys(row).forEach((column) => {
            if (!acc[column]) {
              acc[column] = [];
            }
            acc[column].push(row[column]);
          });
          return acc;
        }, {});

        if(formattedData.Category){
            const uniqueCategories = [...new Set(formattedData.Category)];
  console.log(uniqueCategories); // This will log only unique values
  setApiData((prev)=> ({
    ...prev,
    pyramidCategories: uniqueCategories
  }))
        }

        console.log('display excel data', formattedData)

        setExcelData(formattedData);
        toast.success('Excel file parsed successfully');
      };
      reader.readAsBinaryString(uploadedFile);
    }
  };

  // Function to handle PDF generation (existing)
  const handlePdfDone = useCallback(() => {
    setGeneratingPDF(false);
  }, []);

  const getPdf = async () => {
    try {
      const firstPageElement = componentRef.current;

      if (!firstPageElement) return;

      // Temporarily disable ApexCharts animations
      const charts = firstPageElement.querySelectorAll('.apexcharts-canvas');
      charts.forEach(chart => {
        const chartInstance = window.ApexCharts.getChartByID(chart.id);
        if (chartInstance) {
          chartInstance.updateOptions({
            chart: {
              animations: {
                enabled: false
              }
            }
          }, false, false);
        }
      });

      // Wait for charts to stabilize
      await new Promise(resolve => setTimeout(resolve, 1000));

      const firstCanvas = await html2canvas(firstPageElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        scrollY: -window.scrollY,
        logging: false,
        onclone: (document) => {
          const clonedCharts = document.querySelectorAll('.apexcharts-canvas');
          clonedCharts.forEach(chart => {
            chart.style.visibility = 'visible';
            chart.style.position = 'static';
          });
        }
      });

      const firstImgData = firstCanvas.toDataURL("image/png");
      const pdfWidth = 230;
      const canvasWidth = firstCanvas.width;
      const canvasHeight = firstCanvas.height;
      const pdfHeight = (canvasHeight * pdfWidth) / canvasWidth;

      const pdf = new jsPDF("p", "mm", [pdfHeight, pdfWidth]);
      pdf.addImage(firstImgData, "PNG", 0, 0, pdfWidth, pdfHeight);
      pdf.save("DailySafetyReport.pdf");

      // Re-enable animations after PDF generation
      charts.forEach(chart => {
        const chartInstance = window.ApexCharts.getChartByID(chart.id);
        if (chartInstance) {
          chartInstance.updateOptions({
            chart: {
              animations: {
                enabled: true
              }
            }
          }, false, false);
        }
      });
      toast.success('PDF downloaded successfully');
    } catch (error) {
      toast.error('Error generating PDF');
      console.error('Error generating PDF:', error);
    }
  };

  function downloadPdf() {
    if (generatingPDF) return;

    setGeneratingPDF(true);

    toast.info('PDF Downloading');

    timerRef.current = setTimeout(async () => {
      await getPdf();

      setGeneratingPDF(false); // Reset
    }, 500);
  }

  const factoryID = JSON.parse(localStorage.getItem('userData')).factory.id;
  const userID = JSON.parse(localStorage.getItem('userData')).id;

  const [apiData, setApiData] = useState({
    executiveSummary: {
      totalAlerts: 0,
      avgCompliance: 0,
      peakAlerts: 0,
      highRiskArea: ''
    },
    alertsChartsData: {},
    pyramidCategories: []
  });

  useEffect(() => {
    setLoader(true);
    Promise.all([
      executive_summary_for_report(userID, factoryID, filters),
      get_alerts_for_report(userID, factoryID, filters)
    ]).then(([execData, alertsChartData]) => {
      setApiData((prev) => ({
        ...prev,
        executiveSummary: {
          totalAlerts: execData.total_alerts,
          peakAlerts: execData.peak_alert_time,
          avgCompliance: execData.site_compliance,
          highRiskArea: execData.high_risk_area
        },
        alertsChartsData: alertsChartData
      }));
      setLoader(false);
    });
  }, [filters]);

  return (
    <>
      <button
        className="d-flex align-items-center justify-content-center"
        disabled={loader ? true : false}
        onClick={() => setIsModalOpen(true)} // Open the modal
      >
        {loader ? (
          <div className="spinner-border spinner-border-sm text-danger" role="status"></div>
        ) : (
          <p>Click Pdf</p>
        )}
      </button>

      {/* Modal for file upload */}
      {isModalOpen && (
        <div className="modal" style={{ display: 'block', position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, background: 'rgba(0, 0, 0, 0.5)', zIndex: 10000 }}>
          <div className="modal-dialog" style={{ maxWidth: '500px', margin: 'auto', marginTop: '100px' }}>
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Upload Excel File</h5>
                <button
                  type="button"
                  className="close"
                  onClick={() => setIsModalOpen(false)}
                  aria-label="Close"
                >
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div className="modal-body">
                <input
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleFileChange}
                  className="form-control"
                />
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={() => setIsModalOpen(false)}>
                  Close
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={() => {
                    // Handle file upload
                    if (file) {
                      toast.success('Excel file uploaded');
                    } else {
                      toast.error('Please upload a file');
                    }
                    setIsModalOpen(false);
                  }}
                >
                  Upload
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div
        ref={componentRef}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '1000px',
          pointerEvents: 'none',
          zIndex: -9999,
          background: 'white'
        }}
      >
        <DailySafetyReport apiData={apiData} isPdfRendering={true} filters={filters} />
      </div>
    </>
  );
};

export default PdfRenderer;
